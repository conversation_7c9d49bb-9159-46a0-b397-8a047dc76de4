import{g as <PERSON><PERSON>,_ as <PERSON>}from"./index-Ezoksr9L.js";function on(n,e,r,t,i,a,s){try{var o=n[a](s),u=o.value}catch(l){return void r(l)}o.done?e(u):Promise.resolve(u).then(t,i)}function je(n){return function(){var e=this,r=arguments;return new Promise(function(t,i){var a=n.apply(e,r);function s(u){on(a,t,i,s,o,"next",u)}function o(u){on(a,t,i,s,o,"throw",u)}s(void 0)})}}var un={},ln={},lr,hn;function J(){if(hn)return lr;hn=1;var n=function(e){return e&&e.Math===Math&&e};return lr=n(typeof globalThis=="object"&&globalThis)||n(typeof window=="object"&&window)||n(typeof self=="object"&&self)||n(typeof globalThis=="object"&&globalThis)||n(typeof lr=="object"&&lr)||function(){return this}()||Function("return this")(),lr}var Qr={},Zr,cn;function ee(){return cn||(cn=1,Zr=function(n){try{return!!n()}catch{return!0}}),Zr}var Jr,vn;function Ee(){if(vn)return Jr;vn=1;var n=ee();return Jr=!n(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!==7}),Jr}var et,fn;function Ar(){if(fn)return et;fn=1;var n=ee();return et=!n(function(){var e=(function(){}).bind();return typeof e!="function"||e.hasOwnProperty("prototype")}),et}var rt,gn;function se(){if(gn)return rt;gn=1;var n=Ar(),e=Function.prototype.call;return rt=n?e.bind(e):function(){return e.apply(e,arguments)},rt}var tt={},dn;function Gl(){if(dn)return tt;dn=1;var n={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,r=e&&!n.call({1:2},1);return tt.f=r?function(i){var a=e(this,i);return!!a&&a.enumerable}:n,tt}var it,pn;function ja(){return pn||(pn=1,it=function(n,e){return{enumerable:!(n&1),configurable:!(n&2),writable:!(n&4),value:e}}),it}var at,yn;function re(){if(yn)return at;yn=1;var n=Ar(),e=Function.prototype,r=e.call,t=n&&e.bind.bind(r,r);return at=n?t:function(i){return function(){return r.apply(i,arguments)}},at}var nt,mn;function Ze(){if(mn)return nt;mn=1;var n=re(),e=n({}.toString),r=n("".slice);return nt=function(t){return r(e(t),8,-1)},nt}var st,bn;function Ju(){if(bn)return st;bn=1;var n=re(),e=ee(),r=Ze(),t=Object,i=n("".split);return st=e(function(){return!t("z").propertyIsEnumerable(0)})?function(a){return r(a)==="String"?i(a,""):t(a)}:t,st}var ot,xn;function Je(){return xn||(xn=1,ot=function(n){return n==null}),ot}var ut,On;function Re(){if(On)return ut;On=1;var n=Je(),e=TypeError;return ut=function(r){if(n(r))throw new e("Can't call method on "+r);return r},ut}var lt,Sn;function vr(){if(Sn)return lt;Sn=1;var n=Ju(),e=Re();return lt=function(r){return n(e(r))},lt}var ht,Tn;function te(){if(Tn)return ht;Tn=1;var n=typeof document=="object"&&document.all;return ht=typeof n>"u"&&n!==void 0?function(e){return typeof e=="function"||e===n}:function(e){return typeof e=="function"},ht}var ct,En;function Ce(){if(En)return ct;En=1;var n=te();return ct=function(e){return typeof e=="object"?e!==null:n(e)},ct}var vt,Rn;function er(){if(Rn)return vt;Rn=1;var n=J(),e=te(),r=function(t){return e(t)?t:void 0};return vt=function(t,i){return arguments.length<2?r(n[t]):n[t]&&n[t][i]},vt}var ft,Cn;function Ir(){if(Cn)return ft;Cn=1;var n=re();return ft=n({}.isPrototypeOf),ft}var gt,wn;function Nr(){return wn||(wn=1,gt=typeof navigator<"u"&&String(navigator.userAgent)||""),gt}var dt,Pn;function Ba(){if(Pn)return dt;Pn=1;var n=J(),e=Nr(),r=n.process,t=n.Deno,i=r&&r.versions||t&&t.version,a=i&&i.v8,s,o;return a&&(s=a.split("."),o=s[0]>0&&s[0]<4?1:+(s[0]+s[1])),!o&&e&&(s=e.match(/Edge\/(\d+)/),(!s||s[1]>=74)&&(s=e.match(/Chrome\/(\d+)/),s&&(o=+s[1]))),dt=o,dt}var pt,An;function el(){if(An)return pt;An=1;var n=Ba(),e=ee(),r=J(),t=r.String;return pt=!!Object.getOwnPropertySymbols&&!e(function(){var i=Symbol("symbol detection");return!t(i)||!(Object(i)instanceof Symbol)||!Symbol.sham&&n&&n<41}),pt}var yt,In;function rl(){if(In)return yt;In=1;var n=el();return yt=n&&!Symbol.sham&&typeof Symbol.iterator=="symbol",yt}var mt,Nn;function tl(){if(Nn)return mt;Nn=1;var n=er(),e=te(),r=Ir(),t=rl(),i=Object;return mt=t?function(a){return typeof a=="symbol"}:function(a){var s=n("Symbol");return e(s)&&r(s.prototype,i(a))},mt}var bt,_n;function _r(){if(_n)return bt;_n=1;var n=String;return bt=function(e){try{return n(e)}catch{return"Object"}},bt}var xt,Mn;function Be(){if(Mn)return xt;Mn=1;var n=te(),e=_r(),r=TypeError;return xt=function(t){if(n(t))return t;throw new r(e(t)+" is not a function")},xt}var Ot,qn;function ar(){if(qn)return Ot;qn=1;var n=Be(),e=Je();return Ot=function(r,t){var i=r[t];return e(i)?void 0:n(i)},Ot}var St,Dn;function zl(){if(Dn)return St;Dn=1;var n=se(),e=te(),r=Ce(),t=TypeError;return St=function(i,a){var s,o;if(a==="string"&&e(s=i.toString)&&!r(o=n(s,i))||e(s=i.valueOf)&&!r(o=n(s,i))||a!=="string"&&e(s=i.toString)&&!r(o=n(s,i)))return o;throw new t("Can't convert object to primitive value")},St}var Tt={exports:{}},Et,Vn;function Me(){return Vn||(Vn=1,Et=!1),Et}var Rt,kn;function Fa(){if(kn)return Rt;kn=1;var n=J(),e=Object.defineProperty;return Rt=function(r,t){try{e(n,r,{value:t,configurable:!0,writable:!0})}catch{n[r]=t}return t},Rt}var Ln;function Ua(){if(Ln)return Tt.exports;Ln=1;var n=Me(),e=J(),r=Fa(),t="__core-js_shared__",i=Tt.exports=e[t]||r(t,{});return(i.versions||(i.versions=[])).push({version:"3.37.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"}),Tt.exports}var Ct,jn;function Ga(){if(jn)return Ct;jn=1;var n=Ua();return Ct=function(e,r){return n[e]||(n[e]=r||{})},Ct}var wt,Bn;function Mr(){if(Bn)return wt;Bn=1;var n=Re(),e=Object;return wt=function(r){return e(n(r))},wt}var Pt,Fn;function we(){if(Fn)return Pt;Fn=1;var n=re(),e=Mr(),r=n({}.hasOwnProperty);return Pt=Object.hasOwn||function(i,a){return r(e(i),a)},Pt}var At,Un;function il(){if(Un)return At;Un=1;var n=re(),e=0,r=Math.random(),t=n(1 .toString);return At=function(i){return"Symbol("+(i===void 0?"":i)+")_"+t(++e+r,36)},At}var It,Gn;function ne(){if(Gn)return It;Gn=1;var n=J(),e=Ga(),r=we(),t=il(),i=el(),a=rl(),s=n.Symbol,o=e("wks"),u=a?s.for||s:s&&s.withoutSetter||t;return It=function(l){return r(o,l)||(o[l]=i&&r(s,l)?s[l]:u("Symbol."+l)),o[l]},It}var Nt,zn;function Hl(){if(zn)return Nt;zn=1;var n=se(),e=Ce(),r=tl(),t=ar(),i=zl(),a=ne(),s=TypeError,o=a("toPrimitive");return Nt=function(u,l){if(!e(u)||r(u))return u;var h=t(u,o),v;if(h){if(l===void 0&&(l="default"),v=n(h,u,l),!e(v)||r(v))return v;throw new s("Can't convert object to primitive value")}return l===void 0&&(l="number"),i(u,l)},Nt}var _t,Hn;function al(){if(Hn)return _t;Hn=1;var n=Hl(),e=tl();return _t=function(r){var t=n(r,"string");return e(t)?t:t+""},_t}var Mt,$n;function qr(){if($n)return Mt;$n=1;var n=J(),e=Ce(),r=n.document,t=e(r)&&e(r.createElement);return Mt=function(i){return t?r.createElement(i):{}},Mt}var qt,Yn;function nl(){if(Yn)return qt;Yn=1;var n=Ee(),e=ee(),r=qr();return qt=!n&&!e(function(){return Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a!==7}),qt}var Wn;function Dr(){if(Wn)return Qr;Wn=1;var n=Ee(),e=se(),r=Gl(),t=ja(),i=vr(),a=al(),s=we(),o=nl(),u=Object.getOwnPropertyDescriptor;return Qr.f=n?u:function(h,v){if(h=i(h),v=a(v),o)try{return u(h,v)}catch{}if(s(h,v))return t(!e(r.f,h,v),h[v])},Qr}var Dt={},Vt,Xn;function sl(){if(Xn)return Vt;Xn=1;var n=Ee(),e=ee();return Vt=n&&e(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!==42}),Vt}var kt,Kn;function ue(){if(Kn)return kt;Kn=1;var n=Ce(),e=String,r=TypeError;return kt=function(t){if(n(t))return t;throw new r(e(t)+" is not an object")},kt}var Qn;function $e(){if(Qn)return Dt;Qn=1;var n=Ee(),e=nl(),r=sl(),t=ue(),i=al(),a=TypeError,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,u="enumerable",l="configurable",h="writable";return Dt.f=n?r?function(f,c,g){if(t(f),c=i(c),t(g),typeof f=="function"&&c==="prototype"&&"value"in g&&h in g&&!g[h]){var d=o(f,c);d&&d[h]&&(f[c]=g.value,g={configurable:l in g?g[l]:d[l],enumerable:u in g?g[u]:d[u],writable:!1})}return s(f,c,g)}:s:function(f,c,g){if(t(f),c=i(c),t(g),e)try{return s(f,c,g)}catch{}if("get"in g||"set"in g)throw new a("Accessors not supported");return"value"in g&&(f[c]=g.value),f},Dt}var Lt,Zn;function fr(){if(Zn)return Lt;Zn=1;var n=Ee(),e=$e(),r=ja();return Lt=n?function(t,i,a){return e.f(t,i,r(1,a))}:function(t,i,a){return t[i]=a,t},Lt}var jt={exports:{}},Bt,Jn;function Vr(){if(Jn)return Bt;Jn=1;var n=Ee(),e=we(),r=Function.prototype,t=n&&Object.getOwnPropertyDescriptor,i=e(r,"name"),a=i&&(function(){}).name==="something",s=i&&(!n||n&&t(r,"name").configurable);return Bt={EXISTS:i,PROPER:a,CONFIGURABLE:s},Bt}var Ft,es;function za(){if(es)return Ft;es=1;var n=re(),e=te(),r=Ua(),t=n(Function.toString);return e(r.inspectSource)||(r.inspectSource=function(i){return t(i)}),Ft=r.inspectSource,Ft}var Ut,rs;function $l(){if(rs)return Ut;rs=1;var n=J(),e=te(),r=n.WeakMap;return Ut=e(r)&&/native code/.test(String(r)),Ut}var Gt,ts;function Ha(){if(ts)return Gt;ts=1;var n=Ga(),e=il(),r=n("keys");return Gt=function(t){return r[t]||(r[t]=e(t))},Gt}var zt,is;function $a(){return is||(is=1,zt={}),zt}var Ht,as;function kr(){if(as)return Ht;as=1;var n=$l(),e=J(),r=Ce(),t=fr(),i=we(),a=Ua(),s=Ha(),o=$a(),u="Object already initialized",l=e.TypeError,h=e.WeakMap,v,f,c,g=function(y){return c(y)?f(y):v(y,{})},d=function(y){return function(b){var x;if(!r(b)||(x=f(b)).type!==y)throw new l("Incompatible receiver, "+y+" required");return x}};if(n||a.state){var p=a.state||(a.state=new h);p.get=p.get,p.has=p.has,p.set=p.set,v=function(y,b){if(p.has(y))throw new l(u);return b.facade=y,p.set(y,b),b},f=function(y){return p.get(y)||{}},c=function(y){return p.has(y)}}else{var m=s("state");o[m]=!0,v=function(y,b){if(i(y,m))throw new l(u);return b.facade=y,t(y,m,b),b},f=function(y){return i(y,m)?y[m]:{}},c=function(y){return i(y,m)}}return Ht={set:v,get:f,has:c,enforce:g,getterFor:d},Ht}var ns;function ol(){if(ns)return jt.exports;ns=1;var n=re(),e=ee(),r=te(),t=we(),i=Ee(),a=Vr().CONFIGURABLE,s=za(),o=kr(),u=o.enforce,l=o.get,h=String,v=Object.defineProperty,f=n("".slice),c=n("".replace),g=n([].join),d=i&&!e(function(){return v(function(){},"length",{value:8}).length!==8}),p=String(String).split("String"),m=jt.exports=function(y,b,x){f(h(b),0,7)==="Symbol("&&(b="["+c(h(b),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),x&&x.getter&&(b="get "+b),x&&x.setter&&(b="set "+b),(!t(y,"name")||a&&y.name!==b)&&(i?v(y,"name",{value:b,configurable:!0}):y.name=b),d&&x&&t(x,"arity")&&y.length!==x.arity&&v(y,"length",{value:x.arity});try{x&&t(x,"constructor")&&x.constructor?i&&v(y,"prototype",{writable:!1}):y.prototype&&(y.prototype=void 0)}catch{}var T=u(y);return t(T,"source")||(T.source=g(p,typeof b=="string"?b:"")),y};return Function.prototype.toString=m(function(){return r(this)&&l(this).source||s(this)},"toString"),jt.exports}var $t,ss;function rr(){if(ss)return $t;ss=1;var n=te(),e=$e(),r=ol(),t=Fa();return $t=function(i,a,s,o){o||(o={});var u=o.enumerable,l=o.name!==void 0?o.name:a;if(n(s)&&r(s,l,o),o.global)u?i[a]=s:t(a,s);else{try{o.unsafe?i[a]&&(u=!0):delete i[a]}catch{}u?i[a]=s:e.f(i,a,{value:s,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return i},$t}var Yt={},Wt,os;function Yl(){if(os)return Wt;os=1;var n=Math.ceil,e=Math.floor;return Wt=Math.trunc||function(t){var i=+t;return(i>0?e:n)(i)},Wt}var Xt,us;function Lr(){if(us)return Xt;us=1;var n=Yl();return Xt=function(e){var r=+e;return r!==r||r===0?0:n(r)},Xt}var Kt,ls;function Wl(){if(ls)return Kt;ls=1;var n=Lr(),e=Math.max,r=Math.min;return Kt=function(t,i){var a=n(t);return a<0?e(a+i,0):r(a,i)},Kt}var Qt,hs;function nr(){if(hs)return Qt;hs=1;var n=Lr(),e=Math.min;return Qt=function(r){var t=n(r);return t>0?e(t,9007199254740991):0},Qt}var Zt,cs;function Ya(){if(cs)return Zt;cs=1;var n=nr();return Zt=function(e){return n(e.length)},Zt}var Jt,vs;function ul(){if(vs)return Jt;vs=1;var n=vr(),e=Wl(),r=Ya(),t=function(i){return function(a,s,o){var u=n(a),l=r(u);if(l===0)return!i&&-1;var h=e(o,l),v;if(i&&s!==s){for(;l>h;)if(v=u[h++],v!==v)return!0}else for(;l>h;h++)if((i||h in u)&&u[h]===s)return i||h||0;return!i&&-1}};return Jt={includes:t(!0),indexOf:t(!1)},Jt}var ei,fs;function ll(){if(fs)return ei;fs=1;var n=re(),e=we(),r=vr(),t=ul().indexOf,i=$a(),a=n([].push);return ei=function(s,o){var u=r(s),l=0,h=[],v;for(v in u)!e(i,v)&&e(u,v)&&a(h,v);for(;o.length>l;)e(u,v=o[l++])&&(~t(h,v)||a(h,v));return h},ei}var ri,gs;function Wa(){return gs||(gs=1,ri=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]),ri}var ds;function Xl(){if(ds)return Yt;ds=1;var n=ll(),e=Wa(),r=e.concat("length","prototype");return Yt.f=Object.getOwnPropertyNames||function(i){return n(i,r)},Yt}var ti={},ps;function Kl(){return ps||(ps=1,ti.f=Object.getOwnPropertySymbols),ti}var ii,ys;function Ql(){if(ys)return ii;ys=1;var n=er(),e=re(),r=Xl(),t=Kl(),i=ue(),a=e([].concat);return ii=n("Reflect","ownKeys")||function(o){var u=r.f(i(o)),l=t.f;return l?a(u,l(o)):u},ii}var ai,ms;function Zl(){if(ms)return ai;ms=1;var n=we(),e=Ql(),r=Dr(),t=$e();return ai=function(i,a,s){for(var o=e(a),u=t.f,l=r.f,h=0;h<o.length;h++){var v=o[h];!n(i,v)&&!(s&&n(s,v))&&u(i,v,l(a,v))}},ai}var ni,bs;function hl(){if(bs)return ni;bs=1;var n=ee(),e=te(),r=/#|\.prototype\./,t=function(u,l){var h=a[i(u)];return h===o?!0:h===s?!1:e(l)?n(l):!!l},i=t.normalize=function(u){return String(u).replace(r,".").toLowerCase()},a=t.data={},s=t.NATIVE="N",o=t.POLYFILL="P";return ni=t,ni}var si,xs;function le(){if(xs)return si;xs=1;var n=J(),e=Dr().f,r=fr(),t=rr(),i=Fa(),a=Zl(),s=hl();return si=function(o,u){var l=o.target,h=o.global,v=o.stat,f,c,g,d,p,m;if(h?c=n:v?c=n[l]||i(l,{}):c=n[l]&&n[l].prototype,c)for(g in u){if(p=u[g],o.dontCallGetSet?(m=e(c,g),d=m&&m.value):d=c[g],f=s(h?g:l+(v?".":"#")+g,o.forced),!f&&d!==void 0){if(typeof p==typeof d)continue;a(p,d)}(o.sham||d&&d.sham)&&r(p,"sham",!0),t(c,g,p,o)}},si}var oi,Os;function gr(){if(Os)return oi;Os=1;var n=J(),e=Ze();return oi=e(n.process)==="process",oi}var ui,Ss;function Jl(){if(Ss)return ui;Ss=1;var n=re(),e=Be();return ui=function(r,t,i){try{return n(e(Object.getOwnPropertyDescriptor(r,t)[i]))}catch{}},ui}var li,Ts;function eh(){if(Ts)return li;Ts=1;var n=Ce();return li=function(e){return n(e)||e===null},li}var hi,Es;function rh(){if(Es)return hi;Es=1;var n=eh(),e=String,r=TypeError;return hi=function(t){if(n(t))return t;throw new r("Can't set "+e(t)+" as a prototype")},hi}var ci,Rs;function cl(){if(Rs)return ci;Rs=1;var n=Jl(),e=Ce(),r=Re(),t=rh();return ci=Object.setPrototypeOf||("__proto__"in{}?function(){var i=!1,a={},s;try{s=n(Object.prototype,"__proto__","set"),s(a,[]),i=a instanceof Array}catch{}return function(u,l){return r(u),t(l),e(u)&&(i?s(u,l):u.__proto__=l),u}}():void 0),ci}var vi,Cs;function jr(){if(Cs)return vi;Cs=1;var n=$e().f,e=we(),r=ne(),t=r("toStringTag");return vi=function(i,a,s){i&&!s&&(i=i.prototype),i&&!e(i,t)&&n(i,t,{configurable:!0,value:a})},vi}var fi,ws;function th(){if(ws)return fi;ws=1;var n=ol(),e=$e();return fi=function(r,t,i){return i.get&&n(i.get,t,{getter:!0}),i.set&&n(i.set,t,{setter:!0}),e.f(r,t,i)},fi}var gi,Ps;function ih(){if(Ps)return gi;Ps=1;var n=er(),e=th(),r=ne(),t=Ee(),i=r("species");return gi=function(a){var s=n(a);t&&s&&!s[i]&&e(s,i,{configurable:!0,get:function(){return this}})},gi}var di,As;function ah(){if(As)return di;As=1;var n=Ir(),e=TypeError;return di=function(r,t){if(n(t,r))return r;throw new e("Incorrect invocation")},di}var pi,Is;function nh(){if(Is)return pi;Is=1;var n=ne(),e=n("toStringTag"),r={};return r[e]="z",pi=String(r)==="[object z]",pi}var yi,Ns;function Xa(){if(Ns)return yi;Ns=1;var n=nh(),e=te(),r=Ze(),t=ne(),i=t("toStringTag"),a=Object,s=r(function(){return arguments}())==="Arguments",o=function(u,l){try{return u[l]}catch{}};return yi=n?r:function(u){var l,h,v;return u===void 0?"Undefined":u===null?"Null":typeof(h=o(l=a(u),i))=="string"?h:s?r(l):(v=r(l))==="Object"&&e(l.callee)?"Arguments":v},yi}var mi,_s;function sh(){if(_s)return mi;_s=1;var n=re(),e=ee(),r=te(),t=Xa(),i=er(),a=za(),s=function(){},o=i("Reflect","construct"),u=/^\s*(?:class|function)\b/,l=n(u.exec),h=!u.test(s),v=function(g){if(!r(g))return!1;try{return o(s,[],g),!0}catch{return!1}},f=function(g){if(!r(g))return!1;switch(t(g)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!l(u,a(g))}catch{return!0}};return f.sham=!0,mi=!o||e(function(){var c;return v(v.call)||!v(Object)||!v(function(){c=!0})||c})?f:v,mi}var bi,Ms;function oh(){if(Ms)return bi;Ms=1;var n=sh(),e=_r(),r=TypeError;return bi=function(t){if(n(t))return t;throw new r(e(t)+" is not a constructor")},bi}var xi,qs;function vl(){if(qs)return xi;qs=1;var n=ue(),e=oh(),r=Je(),t=ne(),i=t("species");return xi=function(a,s){var o=n(a).constructor,u;return o===void 0||r(u=n(o)[i])?s:e(u)},xi}var Oi,Ds;function fl(){if(Ds)return Oi;Ds=1;var n=Ar(),e=Function.prototype,r=e.apply,t=e.call;return Oi=typeof Reflect=="object"&&Reflect.apply||(n?t.bind(r):function(){return t.apply(r,arguments)}),Oi}var Si,Vs;function Br(){if(Vs)return Si;Vs=1;var n=Ze(),e=re();return Si=function(r){if(n(r)==="Function")return e(r)},Si}var Ti,ks;function Ka(){if(ks)return Ti;ks=1;var n=Br(),e=Be(),r=Ar(),t=n(n.bind);return Ti=function(i,a){return e(i),a===void 0?i:r?t(i,a):function(){return i.apply(a,arguments)}},Ti}var Ei,Ls;function gl(){if(Ls)return Ei;Ls=1;var n=er();return Ei=n("document","documentElement"),Ei}var Ri,js;function uh(){if(js)return Ri;js=1;var n=re();return Ri=n([].slice),Ri}var Ci,Bs;function lh(){if(Bs)return Ci;Bs=1;var n=TypeError;return Ci=function(e,r){if(e<r)throw new n("Not enough arguments");return e},Ci}var wi,Fs;function dl(){if(Fs)return wi;Fs=1;var n=Nr();return wi=/(?:ipad|iphone|ipod).*applewebkit/i.test(n),wi}var Pi,Us;function pl(){if(Us)return Pi;Us=1;var n=J(),e=fl(),r=Ka(),t=te(),i=we(),a=ee(),s=gl(),o=uh(),u=qr(),l=lh(),h=dl(),v=gr(),f=n.setImmediate,c=n.clearImmediate,g=n.process,d=n.Dispatch,p=n.Function,m=n.MessageChannel,y=n.String,b=0,x={},T="onreadystatechange",E,O,_,V;a(function(){E=n.location});var P=function(R){if(i(x,R)){var A=x[R];delete x[R],A()}},M=function(R){return function(){P(R)}},w=function(R){P(R.data)},L=function(R){n.postMessage(y(R),E.protocol+"//"+E.host)};return(!f||!c)&&(f=function(A){l(arguments.length,1);var U=t(A)?A:p(A),I=o(arguments,1);return x[++b]=function(){e(U,void 0,I)},O(b),b},c=function(A){delete x[A]},v?O=function(R){g.nextTick(M(R))}:d&&d.now?O=function(R){d.now(M(R))}:m&&!h?(_=new m,V=_.port2,_.port1.onmessage=w,O=r(V.postMessage,V)):n.addEventListener&&t(n.postMessage)&&!n.importScripts&&E&&E.protocol!=="file:"&&!a(L)?(O=L,n.addEventListener("message",w,!1)):T in u("script")?O=function(R){s.appendChild(u("script"))[T]=function(){s.removeChild(this),P(R)}}:O=function(R){setTimeout(M(R),0)}),Pi={set:f,clear:c},Pi}var Ai,Gs;function hh(){if(Gs)return Ai;Gs=1;var n=J(),e=Ee(),r=Object.getOwnPropertyDescriptor;return Ai=function(t){if(!e)return n[t];var i=r(n,t);return i&&i.value},Ai}var Ii,zs;function yl(){if(zs)return Ii;zs=1;var n=function(){this.head=null,this.tail=null};return n.prototype={add:function(e){var r={item:e,next:null},t=this.tail;t?t.next=r:this.head=r,this.tail=r},get:function(){var e=this.head;if(e){var r=this.head=e.next;return r===null&&(this.tail=null),e.item}}},Ii=n,Ii}var Ni,Hs;function ch(){if(Hs)return Ni;Hs=1;var n=Nr();return Ni=/ipad|iphone|ipod/i.test(n)&&typeof Pebble<"u",Ni}var _i,$s;function vh(){if($s)return _i;$s=1;var n=Nr();return _i=/web0s(?!.*chrome)/i.test(n),_i}var Mi,Ys;function fh(){if(Ys)return Mi;Ys=1;var n=J(),e=hh(),r=Ka(),t=pl().set,i=yl(),a=dl(),s=ch(),o=vh(),u=gr(),l=n.MutationObserver||n.WebKitMutationObserver,h=n.document,v=n.process,f=n.Promise,c=e("queueMicrotask"),g,d,p,m,y;if(!c){var b=new i,x=function(){var T,E;for(u&&(T=v.domain)&&T.exit();E=b.get();)try{E()}catch(O){throw b.head&&g(),O}T&&T.enter()};!a&&!u&&!o&&l&&h?(d=!0,p=h.createTextNode(""),new l(x).observe(p,{characterData:!0}),g=function(){p.data=d=!d}):!s&&f&&f.resolve?(m=f.resolve(void 0),m.constructor=f,y=r(m.then,m),g=function(){y(x)}):u?g=function(){v.nextTick(x)}:(t=r(t,n),g=function(){t(x)}),c=function(T){b.head||g(),b.add(T)}}return Mi=c,Mi}var qi,Ws;function gh(){return Ws||(Ws=1,qi=function(n,e){try{arguments.length===1?console.error(n):console.error(n,e)}catch{}}),qi}var Di,Xs;function Qa(){return Xs||(Xs=1,Di=function(n){try{return{error:!1,value:n()}}catch(e){return{error:!0,value:e}}}),Di}var Vi,Ks;function dr(){if(Ks)return Vi;Ks=1;var n=J();return Vi=n.Promise,Vi}var ki,Qs;function ml(){return Qs||(Qs=1,ki=typeof Deno=="object"&&Deno&&typeof Deno.version=="object"),ki}var Li,Zs;function dh(){if(Zs)return Li;Zs=1;var n=ml(),e=gr();return Li=!n&&!e&&typeof window=="object"&&typeof document=="object",Li}var ji,Js;function pr(){if(Js)return ji;Js=1;var n=J(),e=dr(),r=te(),t=hl(),i=za(),a=ne(),s=dh(),o=ml(),u=Me(),l=Ba(),h=e&&e.prototype,v=a("species"),f=!1,c=r(n.PromiseRejectionEvent),g=t("Promise",function(){var d=i(e),p=d!==String(e);if(!p&&l===66||u&&!(h.catch&&h.finally))return!0;if(!l||l<51||!/native code/.test(d)){var m=new e(function(x){x(1)}),y=function(x){x(function(){},function(){})},b=m.constructor={};if(b[v]=y,f=m.then(function(){})instanceof y,!f)return!0}return!p&&(s||o)&&!c});return ji={CONSTRUCTOR:g,REJECTION_EVENT:c,SUBCLASSING:f},ji}var Bi={},eo;function yr(){if(eo)return Bi;eo=1;var n=Be(),e=TypeError,r=function(t){var i,a;this.promise=new t(function(s,o){if(i!==void 0||a!==void 0)throw new e("Bad Promise constructor");i=s,a=o}),this.resolve=n(i),this.reject=n(a)};return Bi.f=function(t){return new r(t)},Bi}var ro;function ph(){if(ro)return ln;ro=1;var n=le(),e=Me(),r=gr(),t=J(),i=se(),a=rr(),s=cl(),o=jr(),u=ih(),l=Be(),h=te(),v=Ce(),f=ah(),c=vl(),g=pl().set,d=fh(),p=gh(),m=Qa(),y=yl(),b=kr(),x=dr(),T=pr(),E=yr(),O="Promise",_=T.CONSTRUCTOR,V=T.REJECTION_EVENT,P=T.SUBCLASSING,M=b.getterFor(O),w=b.set,L=x&&x.prototype,R=x,A=L,U=t.TypeError,I=t.document,j=t.process,N=E.f,k=N,B=!!(I&&I.createEvent&&t.dispatchEvent),H="unhandledrejection",X="rejectionhandled",$=0,Q=1,Fe=2,De=1,Ae=2,de,pe,ye,Ie,Ne=function(C){var q;return v(C)&&h(q=C.then)?q:!1},he=function(C,q){var G=q.value,z=q.state===Q,Y=z?C.ok:C.fail,_e=C.resolve,Ve=C.reject,xe=C.domain,Oe,Ye,ur;try{Y?(z||(q.rejection===Ae&&Or(q),q.rejection=De),Y===!0?Oe=G:(xe&&xe.enter(),Oe=Y(G),xe&&(xe.exit(),ur=!0)),Oe===C.promise?Ve(new U("Promise-chain cycle")):(Ye=Ne(Oe))?i(Ye,Oe,_e,Ve):_e(Oe)):Ve(G)}catch(Sr){xe&&!ur&&xe.exit(),Ve(Sr)}},me=function(C,q){C.notified||(C.notified=!0,d(function(){for(var G=C.reactions,z;z=G.get();)he(z,C);C.notified=!1,q&&!C.rejection&&xr(C)}))},Ue=function(C,q,G){var z,Y;B?(z=I.createEvent("Event"),z.promise=q,z.reason=G,z.initEvent(C,!1,!0),t.dispatchEvent(z)):z={promise:q,reason:G},!V&&(Y=t["on"+C])?Y(z):C===H&&p("Unhandled promise rejection",G)},xr=function(C){i(g,t,function(){var q=C.facade,G=C.value,z=or(C),Y;if(z&&(Y=m(function(){r?j.emit("unhandledRejection",G,q):Ue(H,q,G)}),C.rejection=r||or(C)?Ae:De,Y.error))throw Y.value})},or=function(C){return C.rejection!==De&&!C.parent},Or=function(C){i(g,t,function(){var q=C.facade;r?j.emit("rejectionHandled",q):Ue(X,q,C.value)})},fe=function(C,q,G){return function(z){C(q,z,G)}},ce=function(C,q,G){C.done||(C.done=!0,G&&(C=G),C.value=q,C.state=Fe,me(C,!0))},be=function(C,q,G){if(!C.done){C.done=!0,G&&(C=G);try{if(C.facade===q)throw new U("Promise can't be resolved itself");var z=Ne(q);z?d(function(){var Y={done:!1};try{i(z,q,fe(be,Y,C),fe(ce,Y,C))}catch(_e){ce(Y,_e,C)}}):(C.value=q,C.state=Q,me(C,!1))}catch(Y){ce({done:!1},Y,C)}}};if(_&&(R=function(q){f(this,A),l(q),i(de,this);var G=M(this);try{q(fe(be,G),fe(ce,G))}catch(z){ce(G,z)}},A=R.prototype,de=function(q){w(this,{type:O,done:!1,notified:!1,parent:!1,reactions:new y,rejection:!1,state:$,value:void 0})},de.prototype=a(A,"then",function(q,G){var z=M(this),Y=N(c(this,R));return z.parent=!0,Y.ok=h(q)?q:!0,Y.fail=h(G)&&G,Y.domain=r?j.domain:void 0,z.state===$?z.reactions.add(Y):d(function(){he(Y,z)}),Y.promise}),pe=function(){var C=new de,q=M(C);this.promise=C,this.resolve=fe(be,q),this.reject=fe(ce,q)},E.f=N=function(C){return C===R||C===ye?new pe(C):k(C)},!e&&h(x)&&L!==Object.prototype)){Ie=L.then,P||a(L,"then",function(q,G){var z=this;return new R(function(Y,_e){i(Ie,z,Y,_e)}).then(q,G)},{unsafe:!0});try{delete L.constructor}catch{}s&&s(L,A)}return n({global:!0,constructor:!0,wrap:!0,forced:_},{Promise:R}),o(R,O,!1,!0),u(O),ln}var to={},Fi,io;function mr(){return io||(io=1,Fi={}),Fi}var Ui,ao;function yh(){if(ao)return Ui;ao=1;var n=ne(),e=mr(),r=n("iterator"),t=Array.prototype;return Ui=function(i){return i!==void 0&&(e.Array===i||t[r]===i)},Ui}var Gi,no;function bl(){if(no)return Gi;no=1;var n=Xa(),e=ar(),r=Je(),t=mr(),i=ne(),a=i("iterator");return Gi=function(s){if(!r(s))return e(s,a)||e(s,"@@iterator")||t[n(s)]},Gi}var zi,so;function mh(){if(so)return zi;so=1;var n=se(),e=Be(),r=ue(),t=_r(),i=bl(),a=TypeError;return zi=function(s,o){var u=arguments.length<2?i(s):o;if(e(u))return r(n(u,s));throw new a(t(s)+" is not iterable")},zi}var Hi,oo;function bh(){if(oo)return Hi;oo=1;var n=se(),e=ue(),r=ar();return Hi=function(t,i,a){var s,o;e(t);try{if(s=r(t,"return"),!s){if(i==="throw")throw a;return a}s=n(s,t)}catch(u){o=!0,s=u}if(i==="throw")throw a;if(o)throw s;return e(s),a},Hi}var $i,uo;function xl(){if(uo)return $i;uo=1;var n=Ka(),e=se(),r=ue(),t=_r(),i=yh(),a=Ya(),s=Ir(),o=mh(),u=bl(),l=bh(),h=TypeError,v=function(c,g){this.stopped=c,this.result=g},f=v.prototype;return $i=function(c,g,d){var p=d&&d.that,m=!!(d&&d.AS_ENTRIES),y=!!(d&&d.IS_RECORD),b=!!(d&&d.IS_ITERATOR),x=!!(d&&d.INTERRUPTED),T=n(g,p),E,O,_,V,P,M,w,L=function(A){return E&&l(E,"normal",A),new v(!0,A)},R=function(A){return m?(r(A),x?T(A[0],A[1],L):T(A[0],A[1])):x?T(A,L):T(A)};if(y)E=c.iterator;else if(b)E=c;else{if(O=u(c),!O)throw new h(t(c)+" is not iterable");if(i(O)){for(_=0,V=a(c);V>_;_++)if(P=R(c[_]),P&&s(f,P))return P;return new v(!1)}E=o(c,O)}for(M=y?c.next:E.next;!(w=e(M,E)).done;){try{P=R(w.value)}catch(A){l(E,"throw",A)}if(typeof P=="object"&&P&&s(f,P))return P}return new v(!1)},$i}var Yi,lo;function xh(){if(lo)return Yi;lo=1;var n=ne(),e=n("iterator"),r=!1;try{var t=0,i={next:function(){return{done:!!t++}},return:function(){r=!0}};i[e]=function(){return this},Array.from(i,function(){throw 2})}catch{}return Yi=function(a,s){try{if(!s&&!r)return!1}catch{return!1}var o=!1;try{var u={};u[e]=function(){return{next:function(){return{done:o=!0}}}},a(u)}catch{}return o},Yi}var Wi,ho;function Ol(){if(ho)return Wi;ho=1;var n=dr(),e=xh(),r=pr().CONSTRUCTOR;return Wi=r||!e(function(t){n.all(t).then(void 0,function(){})}),Wi}var co;function Oh(){if(co)return to;co=1;var n=le(),e=se(),r=Be(),t=yr(),i=Qa(),a=xl(),s=Ol();return n({target:"Promise",stat:!0,forced:s},{all:function(u){var l=this,h=t.f(l),v=h.resolve,f=h.reject,c=i(function(){var g=r(l.resolve),d=[],p=0,m=1;a(u,function(y){var b=p++,x=!1;m++,e(g,l,y).then(function(T){x||(x=!0,d[b]=T,--m||v(d))},f)}),--m||v(d)});return c.error&&f(c.value),h.promise}}),to}var vo={},fo;function Sh(){if(fo)return vo;fo=1;var n=le(),e=Me(),r=pr().CONSTRUCTOR,t=dr(),i=er(),a=te(),s=rr(),o=t&&t.prototype;if(n({target:"Promise",proto:!0,forced:r,real:!0},{catch:function(l){return this.then(void 0,l)}}),!e&&a(t)){var u=i("Promise").prototype.catch;o.catch!==u&&s(o,"catch",u,{unsafe:!0})}return vo}var go={},po;function Th(){if(po)return go;po=1;var n=le(),e=se(),r=Be(),t=yr(),i=Qa(),a=xl(),s=Ol();return n({target:"Promise",stat:!0,forced:s},{race:function(u){var l=this,h=t.f(l),v=h.reject,f=i(function(){var c=r(l.resolve);a(u,function(g){e(c,l,g).then(h.resolve,v)})});return f.error&&v(f.value),h.promise}}),go}var yo={},mo;function Eh(){if(mo)return yo;mo=1;var n=le(),e=yr(),r=pr().CONSTRUCTOR;return n({target:"Promise",stat:!0,forced:r},{reject:function(i){var a=e.f(this),s=a.reject;return s(i),a.promise}}),yo}var bo={},Xi,xo;function Rh(){if(xo)return Xi;xo=1;var n=ue(),e=Ce(),r=yr();return Xi=function(t,i){if(n(t),e(i)&&i.constructor===t)return i;var a=r.f(t),s=a.resolve;return s(i),a.promise},Xi}var Oo;function Ch(){if(Oo)return bo;Oo=1;var n=le(),e=er(),r=Me(),t=dr(),i=pr().CONSTRUCTOR,a=Rh(),s=e("Promise"),o=r&&!i;return n({target:"Promise",stat:!0,forced:r||i},{resolve:function(l){return a(o&&this===s?t:this,l)}}),bo}var So;function wh(){return So||(So=1,ph(),Oh(),Sh(),Th(),Eh(),Ch()),un}wh();var To={},Eo={},Ki,Ro;function qe(){if(Ro)return Ki;Ro=1;var n=Xa(),e=String;return Ki=function(r){if(n(r)==="Symbol")throw new TypeError("Cannot convert a Symbol value to a string");return e(r)},Ki}var Qi,Co;function Sl(){if(Co)return Qi;Co=1;var n=ue();return Qi=function(){var e=n(this),r="";return e.hasIndices&&(r+="d"),e.global&&(r+="g"),e.ignoreCase&&(r+="i"),e.multiline&&(r+="m"),e.dotAll&&(r+="s"),e.unicode&&(r+="u"),e.unicodeSets&&(r+="v"),e.sticky&&(r+="y"),r},Qi}var Zi,wo;function Tl(){if(wo)return Zi;wo=1;var n=ee(),e=J(),r=e.RegExp,t=n(function(){var s=r("a","y");return s.lastIndex=2,s.exec("abcd")!==null}),i=t||n(function(){return!r("a","y").sticky}),a=t||n(function(){var s=r("^r","gy");return s.lastIndex=2,s.exec("str")!==null});return Zi={BROKEN_CARET:a,MISSED_STICKY:i,UNSUPPORTED_Y:t},Zi}var Ji={},ea,Po;function Ph(){if(Po)return ea;Po=1;var n=ll(),e=Wa();return ea=Object.keys||function(t){return n(t,e)},ea}var Ao;function Ah(){if(Ao)return Ji;Ao=1;var n=Ee(),e=sl(),r=$e(),t=ue(),i=vr(),a=Ph();return Ji.f=n&&!e?Object.defineProperties:function(o,u){t(o);for(var l=i(u),h=a(u),v=h.length,f=0,c;v>f;)r.f(o,c=h[f++],l[c]);return o},Ji}var ra,Io;function Fr(){if(Io)return ra;Io=1;var n=ue(),e=Ah(),r=Wa(),t=$a(),i=gl(),a=qr(),s=Ha(),o=">",u="<",l="prototype",h="script",v=s("IE_PROTO"),f=function(){},c=function(y){return u+h+o+y+u+"/"+h+o},g=function(y){y.write(c("")),y.close();var b=y.parentWindow.Object;return y=null,b},d=function(){var y=a("iframe"),b="java"+h+":",x;return y.style.display="none",i.appendChild(y),y.src=String(b),x=y.contentWindow.document,x.open(),x.write(c("document.F=Object")),x.close(),x.F},p,m=function(){try{p=new ActiveXObject("htmlfile")}catch{}m=typeof document<"u"?document.domain&&p?g(p):d():g(p);for(var y=r.length;y--;)delete m[l][r[y]];return m()};return t[v]=!0,ra=Object.create||function(b,x){var T;return b!==null?(f[l]=n(b),T=new f,f[l]=null,T[v]=b):T=m(),x===void 0?T:e.f(T,x)},ra}var ta,No;function Ih(){if(No)return ta;No=1;var n=ee(),e=J(),r=e.RegExp;return ta=n(function(){var t=r(".","s");return!(t.dotAll&&t.test(`
`)&&t.flags==="s")}),ta}var ia,_o;function Nh(){if(_o)return ia;_o=1;var n=ee(),e=J(),r=e.RegExp;return ia=n(function(){var t=r("(?<a>b)","g");return t.exec("b").groups.a!=="b"||"b".replace(t,"$<a>c")!=="bc"}),ia}var aa,Mo;function Za(){if(Mo)return aa;Mo=1;var n=se(),e=re(),r=qe(),t=Sl(),i=Tl(),a=Ga(),s=Fr(),o=kr().get,u=Ih(),l=Nh(),h=a("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,f=v,c=e("".charAt),g=e("".indexOf),d=e("".replace),p=e("".slice),m=function(){var T=/a/,E=/b*/g;return n(v,T,"a"),n(v,E,"a"),T.lastIndex!==0||E.lastIndex!==0}(),y=i.BROKEN_CARET,b=/()??/.exec("")[1]!==void 0,x=m||b||y||u||l;return x&&(f=function(E){var O=this,_=o(O),V=r(E),P=_.raw,M,w,L,R,A,U,I;if(P)return P.lastIndex=O.lastIndex,M=n(f,P,V),O.lastIndex=P.lastIndex,M;var j=_.groups,N=y&&O.sticky,k=n(t,O),B=O.source,H=0,X=V;if(N&&(k=d(k,"y",""),g(k,"g")===-1&&(k+="g"),X=p(V,O.lastIndex),O.lastIndex>0&&(!O.multiline||O.multiline&&c(V,O.lastIndex-1)!==`
`)&&(B="(?: "+B+")",X=" "+X,H++),w=new RegExp("^(?:"+B+")",k)),b&&(w=new RegExp("^"+B+"$(?!\\s)",k)),m&&(L=O.lastIndex),R=n(v,N?w:O,X),N?R?(R.input=p(R.input,H),R[0]=p(R[0],H),R.index=O.lastIndex,O.lastIndex+=R[0].length):O.lastIndex=0:m&&R&&(O.lastIndex=O.global?R.index+R[0].length:L),b&&R&&R.length>1&&n(h,R[0],w,function(){for(A=1;A<arguments.length-2;A++)arguments[A]===void 0&&(R[A]=void 0)}),R&&j)for(R.groups=U=s(null),A=0;A<j.length;A++)I=j[A],U[I[0]]=R[I[1]];return R}),aa=f,aa}var qo;function _h(){if(qo)return Eo;qo=1;var n=le(),e=Za();return n({target:"RegExp",proto:!0,forced:/./.exec!==e},{exec:e}),Eo}var na,Do;function Ja(){if(Do)return na;Do=1,_h();var n=se(),e=rr(),r=Za(),t=ee(),i=ne(),a=fr(),s=i("species"),o=RegExp.prototype;return na=function(u,l,h,v){var f=i(u),c=!t(function(){var m={};return m[f]=function(){return 7},""[u](m)!==7}),g=c&&!t(function(){var m=!1,y=/a/;return u==="split"&&(y={},y.constructor={},y.constructor[s]=function(){return y},y.flags="",y[f]=/./[f]),y.exec=function(){return m=!0,null},y[f](""),!m});if(!c||!g||h){var d=/./[f],p=l(f,""[u],function(m,y,b,x,T){var E=y.exec;return E===r||E===o.exec?c&&!T?{done:!0,value:n(d,y,b,x)}:{done:!0,value:n(m,b,y,x)}:{done:!1}});e(String.prototype,u,p[0]),e(o,f,p[1])}v&&a(o[f],"sham",!0)},na}var sa,Vo;function Mh(){if(Vo)return sa;Vo=1;var n=re(),e=Lr(),r=qe(),t=Re(),i=n("".charAt),a=n("".charCodeAt),s=n("".slice),o=function(u){return function(l,h){var v=r(t(l)),f=e(h),c=v.length,g,d;return f<0||f>=c?u?"":void 0:(g=a(v,f),g<55296||g>56319||f+1===c||(d=a(v,f+1))<56320||d>57343?u?i(v,f):g:u?s(v,f,f+2):(g-55296<<10)+(d-56320)+65536)}};return sa={codeAt:o(!1),charAt:o(!0)},sa}var oa,ko;function en(){if(ko)return oa;ko=1;var n=Mh().charAt;return oa=function(e,r,t){return r+(t?n(e,r).length:1)},oa}var ua,Lo;function rn(){if(Lo)return ua;Lo=1;var n=se(),e=ue(),r=te(),t=Ze(),i=Za(),a=TypeError;return ua=function(s,o){var u=s.exec;if(r(u)){var l=n(u,s,o);return l!==null&&e(l),l}if(t(s)==="RegExp")return n(i,s,o);throw new a("RegExp#exec called on incompatible receiver")},ua}var jo;function qh(){if(jo)return To;jo=1;var n=se(),e=Ja(),r=ue(),t=Je(),i=nr(),a=qe(),s=Re(),o=ar(),u=en(),l=rn();return e("match",function(h,v,f){return[function(g){var d=s(this),p=t(g)?void 0:o(g,h);return p?n(p,g,d):new RegExp(g)[h](a(d))},function(c){var g=r(this),d=a(c),p=f(v,g,d);if(p.done)return p.value;if(!g.global)return l(g,d);var m=g.unicode;g.lastIndex=0;for(var y=[],b=0,x;(x=l(g,d))!==null;){var T=a(x[0]);y[b]=T,T===""&&(g.lastIndex=u(d,i(g.lastIndex),m)),b++}return b===0?null:y}]}),To}qh();var Bo={},la,Fo;function Dh(){if(Fo)return la;Fo=1;var n=re(),e=Mr(),r=Math.floor,t=n("".charAt),i=n("".replace),a=n("".slice),s=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,o=/\$([$&'`]|\d{1,2})/g;return la=function(u,l,h,v,f,c){var g=h+u.length,d=v.length,p=o;return f!==void 0&&(f=e(f),p=s),i(c,p,function(m,y){var b;switch(t(y,0)){case"$":return"$";case"&":return u;case"`":return a(l,0,h);case"'":return a(l,g);case"<":b=f[a(y,1,-1)];break;default:var x=+y;if(x===0)return m;if(x>d){var T=r(x/10);return T===0?m:T<=d?v[T-1]===void 0?t(y,1):v[T-1]+t(y,1):m}b=v[x-1]}return b===void 0?"":b})},la}var Uo;function Vh(){if(Uo)return Bo;Uo=1;var n=fl(),e=se(),r=re(),t=Ja(),i=ee(),a=ue(),s=te(),o=Je(),u=Lr(),l=nr(),h=qe(),v=Re(),f=en(),c=ar(),g=Dh(),d=rn(),p=ne(),m=p("replace"),y=Math.max,b=Math.min,x=r([].concat),T=r([].push),E=r("".indexOf),O=r("".slice),_=function(w){return w===void 0?w:String(w)},V=function(){return"a".replace(/./,"$0")==="$0"}(),P=function(){return/./[m]?/./[m]("a","$0")==="":!1}(),M=!i(function(){var w=/./;return w.exec=function(){var L=[];return L.groups={a:"7"},L},"".replace(w,"$<a>")!=="7"});return t("replace",function(w,L,R){var A=P?"$":"$0";return[function(I,j){var N=v(this),k=o(I)?void 0:c(I,m);return k?e(k,I,N,j):e(L,h(N),I,j)},function(U,I){var j=a(this),N=h(U);if(typeof I=="string"&&E(I,A)===-1&&E(I,"$<")===-1){var k=R(L,j,N,I);if(k.done)return k.value}var B=s(I);B||(I=h(I));var H=j.global,X;H&&(X=j.unicode,j.lastIndex=0);for(var $=[],Q;Q=d(j,N),!(Q===null||(T($,Q),!H));){var Fe=h(Q[0]);Fe===""&&(j.lastIndex=f(N,l(j.lastIndex),X))}for(var De="",Ae=0,de=0;de<$.length;de++){Q=$[de];for(var pe=h(Q[0]),ye=y(b(u(Q.index),N.length),0),Ie=[],Ne,he=1;he<Q.length;he++)T(Ie,_(Q[he]));var me=Q.groups;if(B){var Ue=x([pe],Ie,ye,N);me!==void 0&&T(Ue,me),Ne=h(n(I,void 0,Ue))}else Ne=g(pe,N,ye,Ie,me,I);ye>=Ae&&(De+=O(N,Ae,ye)+Ne,Ae=ye+pe.length)}return De+O(N,Ae)}]},!M||!V||P),Bo}Vh();var Go={},ha,zo;function kh(){if(zo)return ha;zo=1;var n=Ce(),e=Ze(),r=ne(),t=r("match");return ha=function(i){var a;return n(i)&&((a=i[t])!==void 0?!!a:e(i)==="RegExp")},ha}var ca,Ho;function tn(){if(Ho)return ca;Ho=1;var n=kh(),e=TypeError;return ca=function(r){if(n(r))throw new e("The method doesn't accept regular expressions");return r},ca}var va,$o;function an(){if($o)return va;$o=1;var n=ne(),e=n("match");return va=function(r){var t=/./;try{"/./"[r](t)}catch{try{return t[e]=!1,"/./"[r](t)}catch{}}return!1},va}var Yo;function Lh(){if(Yo)return Go;Yo=1;var n=le(),e=Br(),r=Dr().f,t=nr(),i=qe(),a=tn(),s=Re(),o=an(),u=Me(),l=e("".slice),h=Math.min,v=o("startsWith"),f=!u&&!v&&!!function(){var c=r(String.prototype,"startsWith");return c&&!c.writable}();return n({target:"String",proto:!0,forced:!f&&!v},{startsWith:function(g){var d=i(s(this));a(g);var p=t(h(arguments.length>1?arguments[1]:void 0,d.length)),m=i(g);return l(d,p,p+m.length)===m}}),Go}Lh();var fa,Wo;function jh(){if(Wo)return fa;Wo=1;var n=ne(),e=Fr(),r=$e().f,t=n("unscopables"),i=Array.prototype;return i[t]===void 0&&r(i,t,{configurable:!0,value:e(null)}),fa=function(a){i[t][a]=!0},fa}var ga,Xo;function Bh(){if(Xo)return ga;Xo=1;var n=ee();return ga=!n(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),ga}var da,Ko;function El(){if(Ko)return da;Ko=1;var n=we(),e=te(),r=Mr(),t=Ha(),i=Bh(),a=t("IE_PROTO"),s=Object,o=s.prototype;return da=i?s.getPrototypeOf:function(u){var l=r(u);if(n(l,a))return l[a];var h=l.constructor;return e(h)&&l instanceof h?h.prototype:l instanceof s?o:null},da}var pa,Qo;function Rl(){if(Qo)return pa;Qo=1;var n=ee(),e=te(),r=Ce(),t=Fr(),i=El(),a=rr(),s=ne(),o=Me(),u=s("iterator"),l=!1,h,v,f;[].keys&&(f=[].keys(),"next"in f?(v=i(i(f)),v!==Object.prototype&&(h=v)):l=!0);var c=!r(h)||n(function(){var g={};return h[u].call(g)!==g});return c?h={}:o&&(h=t(h)),e(h[u])||a(h,u,function(){return this}),pa={IteratorPrototype:h,BUGGY_SAFARI_ITERATORS:l},pa}var ya,Zo;function Fh(){if(Zo)return ya;Zo=1;var n=Rl().IteratorPrototype,e=Fr(),r=ja(),t=jr(),i=mr(),a=function(){return this};return ya=function(s,o,u,l){var h=o+" Iterator";return s.prototype=e(n,{next:r(+!l,u)}),t(s,h,!1,!0),i[h]=a,s},ya}var ma,Jo;function Uh(){if(Jo)return ma;Jo=1;var n=le(),e=se(),r=Me(),t=Vr(),i=te(),a=Fh(),s=El(),o=cl(),u=jr(),l=fr(),h=rr(),v=ne(),f=mr(),c=Rl(),g=t.PROPER,d=t.CONFIGURABLE,p=c.IteratorPrototype,m=c.BUGGY_SAFARI_ITERATORS,y=v("iterator"),b="keys",x="values",T="entries",E=function(){return this};return ma=function(O,_,V,P,M,w,L){a(V,_,P);var R=function($){if($===M&&N)return N;if(!m&&$&&$ in I)return I[$];switch($){case b:return function(){return new V(this,$)};case x:return function(){return new V(this,$)};case T:return function(){return new V(this,$)}}return function(){return new V(this)}},A=_+" Iterator",U=!1,I=O.prototype,j=I[y]||I["@@iterator"]||M&&I[M],N=!m&&j||R(M),k=_==="Array"&&I.entries||j,B,H,X;if(k&&(B=s(k.call(new O)),B!==Object.prototype&&B.next&&(!r&&s(B)!==p&&(o?o(B,p):i(B[y])||h(B,y,E)),u(B,A,!0,!0),r&&(f[A]=E))),g&&M===x&&j&&j.name!==x&&(!r&&d?l(I,"name",x):(U=!0,N=function(){return e(j,this)})),M)if(H={values:R(x),keys:w?N:R(b),entries:R(T)},L)for(X in H)(m||U||!(X in I))&&h(I,X,H[X]);else n({target:_,proto:!0,forced:m||U},H);return(!r||L)&&I[y]!==N&&h(I,y,N,{name:M}),f[_]=N,H},ma}var ba,eu;function Gh(){return eu||(eu=1,ba=function(n,e){return{value:n,done:e}}),ba}var xa,ru;function Cl(){if(ru)return xa;ru=1;var n=vr(),e=jh(),r=mr(),t=kr(),i=$e().f,a=Uh(),s=Gh(),o=Me(),u=Ee(),l="Array Iterator",h=t.set,v=t.getterFor(l);xa=a(Array,"Array",function(c,g){h(this,{type:l,target:n(c),index:0,kind:g})},function(){var c=v(this),g=c.target,d=c.index++;if(!g||d>=g.length)return c.target=void 0,s(void 0,!0);switch(c.kind){case"keys":return s(d,!1);case"values":return s(g[d],!1)}return s([d,g[d]],!1)},"values");var f=r.Arguments=r.Array;if(e("keys"),e("values"),e("entries"),!o&&u&&f.name!=="values")try{i(f,"name",{value:"values"})}catch{}return xa}Cl();var tu={},Oa,iu;function zh(){return iu||(iu=1,Oa={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}),Oa}var Sa,au;function Hh(){if(au)return Sa;au=1;var n=qr(),e=n("span").classList,r=e&&e.constructor&&e.constructor.prototype;return Sa=r===Object.prototype?void 0:r,Sa}var nu;function $h(){if(nu)return tu;nu=1;var n=J(),e=zh(),r=Hh(),t=Cl(),i=fr(),a=jr(),s=ne(),o=s("iterator"),u=t.values,l=function(v,f){if(v){if(v[o]!==u)try{i(v,o,u)}catch{v[o]=u}if(a(v,f,!0),e[f]){for(var c in t)if(v[c]!==t[c])try{i(v,c,t[c])}catch{v[c]=t[c]}}}};for(var h in e)l(n[h]&&n[h].prototype,h);return l(r,"DOMTokenList"),tu}$h();var su={},Ta,ou;function Yh(){if(ou)return Ta;ou=1;var n=Be(),e=Mr(),r=Ju(),t=Ya(),i=TypeError,a="Reduce of empty array with no initial value",s=function(o){return function(u,l,h,v){var f=e(u),c=r(f),g=t(f);if(n(l),g===0&&h<2)throw new i(a);var d=o?g-1:0,p=o?-1:1;if(h<2)for(;;){if(d in c){v=c[d],d+=p;break}if(d+=p,o?d<0:g<=d)throw new i(a)}for(;o?d>=0:g>d;d+=p)d in c&&(v=l(v,c[d],d,f));return v}};return Ta={left:s(!1),right:s(!0)},Ta}var Ea,uu;function wl(){if(uu)return Ea;uu=1;var n=ee();return Ea=function(e,r){var t=[][e];return!!t&&n(function(){t.call(null,r||function(){return 1},1)})},Ea}var lu;function Wh(){if(lu)return su;lu=1;var n=le(),e=Yh().left,r=wl(),t=Ba(),i=gr(),a=!i&&t>79&&t<83,s=a||!r("reduce");return n({target:"Array",proto:!0,forced:s},{reduce:function(u){var l=arguments.length;return e(this,u,l,l>1?arguments[1]:void 0)}}),su}Wh();var hu={},cu;function Xh(){if(cu)return hu;cu=1;var n=le(),e=Br(),r=Dr().f,t=nr(),i=qe(),a=tn(),s=Re(),o=an(),u=Me(),l=e("".slice),h=Math.min,v=o("endsWith"),f=!u&&!v&&!!function(){var c=r(String.prototype,"endsWith");return c&&!c.writable}();return n({target:"String",proto:!0,forced:!f&&!v},{endsWith:function(g){var d=i(s(this));a(g);var p=arguments.length>1?arguments[1]:void 0,m=d.length,y=p===void 0?m:h(t(p),m),b=i(g);return l(d,y-b.length,y)===b}}),hu}Xh();var vu={},fu;function Kh(){if(fu)return vu;fu=1;var n=se(),e=re(),r=Ja(),t=ue(),i=Je(),a=Re(),s=vl(),o=en(),u=nr(),l=qe(),h=ar(),v=rn(),f=Tl(),c=ee(),g=f.UNSUPPORTED_Y,d=4294967295,p=Math.min,m=e([].push),y=e("".slice),b=!c(function(){var T=/(?:)/,E=T.exec;T.exec=function(){return E.apply(this,arguments)};var O="ab".split(T);return O.length!==2||O[0]!=="a"||O[1]!=="b"}),x="abbc".split(/(b)*/)[1]==="c"||"test".split(/(?:)/,-1).length!==4||"ab".split(/(?:ab)*/).length!==2||".".split(/(.?)(.?)/).length!==4||".".split(/()()/).length>1||"".split(/.?/).length;return r("split",function(T,E,O){var _="0".split(void 0,0).length?function(V,P){return V===void 0&&P===0?[]:n(E,this,V,P)}:E;return[function(P,M){var w=a(this),L=i(P)?void 0:h(P,T);return L?n(L,P,w,M):n(_,l(w),P,M)},function(V,P){var M=t(this),w=l(V);if(!x){var L=O(_,M,w,P,_!==E);if(L.done)return L.value}var R=s(M,RegExp),A=M.unicode,U=(M.ignoreCase?"i":"")+(M.multiline?"m":"")+(M.unicode?"u":"")+(g?"g":"y"),I=new R(g?"^(?:"+M.source+")":M,U),j=P===void 0?d:P>>>0;if(j===0)return[];if(w.length===0)return v(I,w)===null?[w]:[];for(var N=0,k=0,B=[];k<w.length;){I.lastIndex=g?0:k;var H=v(I,g?y(w,k):w),X;if(H===null||(X=p(u(I.lastIndex+(g?k:0)),w.length))===N)k=o(w,k,A);else{if(m(B,y(w,N,k)),B.length===j)return B;for(var $=1;$<=H.length-1;$++)if(m(B,H[$]),B.length===j)return B;k=N=X}}return m(B,y(w,N)),B}]},x||!b,g),vu}Kh();var hr={exports:{}},Ke={exports:{}},Qh=Ke.exports,gu;function Zh(){return gu||(gu=1,(function(){var n,e,r,t,i,a;typeof performance<"u"&&performance!==null&&performance.now?Ke.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(Ke.exports=function(){return(n()-i)/1e6},e=process.hrtime,n=function(){var s;return s=e(),s[0]*1e9+s[1]},t=n(),a=process.uptime()*1e9,i=t-a):Date.now?(Ke.exports=function(){return Date.now()-r},r=Date.now()):(Ke.exports=function(){return new Date().getTime()-r},r=new Date().getTime())}).call(Qh)),Ke.exports}var du;function Jh(){if(du)return hr.exports;du=1;for(var n=Zh(),e=typeof window>"u"?globalThis:window,r=["moz","webkit"],t="AnimationFrame",i=e["request"+t],a=e["cancel"+t]||e["cancelRequest"+t],s=0;!i&&s<r.length;s++)i=e[r[s]+"Request"+t],a=e[r[s]+"Cancel"+t]||e[r[s]+"CancelRequest"+t];if(!i||!a){var o=0,u=0,l=[],h=1e3/60;i=function(v){if(l.length===0){var f=n(),c=Math.max(0,h-(f-o));o=c+f,setTimeout(function(){var g=l.slice(0);l.length=0;for(var d=0;d<g.length;d++)if(!g[d].cancelled)try{g[d].callback(o)}catch(p){setTimeout(function(){throw p},0)}},Math.round(c))}return l.push({handle:++u,callback:v,cancelled:!1}),u},a=function(v){for(var f=0;f<l.length;f++)l[f].handle===v&&(l[f].cancelled=!0)}}return hr.exports=function(v){return i.call(e,v)},hr.exports.cancel=function(){a.apply(e,arguments)},hr.exports.polyfill=function(v){v||(v=e),v.requestAnimationFrame=i,v.cancelAnimationFrame=a},hr.exports}var ec=Jh();const Ra=Zu(ec);var pu={},Ca,yu;function Pl(){return yu||(yu=1,Ca=`	
\v\f\r                　\u2028\u2029\uFEFF`),Ca}var wa,mu;function rc(){if(mu)return wa;mu=1;var n=re(),e=Re(),r=qe(),t=Pl(),i=n("".replace),a=RegExp("^["+t+"]+"),s=RegExp("(^|[^"+t+"])["+t+"]+$"),o=function(u){return function(l){var h=r(e(l));return u&1&&(h=i(h,a,"")),u&2&&(h=i(h,s,"$1")),h}};return wa={start:o(1),end:o(2),trim:o(3)},wa}var Pa,bu;function tc(){if(bu)return Pa;bu=1;var n=Vr().PROPER,e=ee(),r=Pl(),t="​᠎";return Pa=function(i){return e(function(){return!!r[i]()||t[i]()!==t||n&&r[i].name!==i})},Pa}var xu;function ic(){if(xu)return pu;xu=1;var n=le(),e=rc().trim,r=tc();return n({target:"String",proto:!0,forced:r("trim")},{trim:function(){return e(this)}}),pu}ic();var Aa,Ou;function ac(){return Ou||(Ou=1,Aa=function(n){this.ok=!1,this.alpha=1,n.charAt(0)=="#"&&(n=n.substr(1,6)),n=n.replace(/ /g,""),n=n.toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};n=e[n]||n;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3]),parseFloat(u[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(u){return[parseInt(u[1],16),parseInt(u[2],16),parseInt(u[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(u){return[parseInt(u[1]+u[1],16),parseInt(u[2]+u[2],16),parseInt(u[3]+u[3],16)]}}],t=0;t<r.length;t++){var i=r[t].re,a=r[t].process,s=i.exec(n);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var u=this.r.toString(16),l=this.g.toString(16),h=this.b.toString(16);return u.length==1&&(u="0"+u),l.length==1&&(l="0"+l),h.length==1&&(h="0"+h),"#"+u+l+h},this.getHelpXML=function(){for(var u=new Array,l=0;l<r.length;l++)for(var h=r[l].example,v=0;v<h.length;v++)u[u.length]=h[v];for(var f in e)u[u.length]=f;var c=document.createElement("ul");c.setAttribute("id","rgbcolor-examples");for(var l=0;l<u.length;l++)try{var g=document.createElement("li"),d=new RGBColor(u[l]),p=document.createElement("div");p.style.cssText="margin: 3px; border: 1px solid black; background:"+d.toHex()+"; color:"+d.toHex(),p.appendChild(document.createTextNode("test"));var m=document.createTextNode(" "+u[l]+" -> "+d.toRGB()+" -> "+d.toHex());g.appendChild(p),g.appendChild(m),c.appendChild(g)}catch{}return c}}),Aa}var nc=ac();const qa=Zu(nc);var Su={},Tu;function sc(){if(Tu)return Su;Tu=1;var n=le(),e=Br(),r=ul().indexOf,t=wl(),i=e([].indexOf),a=!!i&&1/i([1],1,-0)<0,s=a||!t("indexOf");return n({target:"Array",proto:!0,forced:s},{indexOf:function(u){var l=arguments.length>1?arguments[1]:void 0;return a?i(this,u,l)||0:r(this,u,l)}}),Su}sc();var Eu={},Ru;function oc(){if(Ru)return Eu;Ru=1;var n=le(),e=re(),r=tn(),t=Re(),i=qe(),a=an(),s=e("".indexOf);return n({target:"String",proto:!0,forced:!a("includes")},{includes:function(u){return!!~s(i(t(this)),i(r(u)),arguments.length>1?arguments[1]:void 0)}}),Eu}oc();var Cu={},Ia,wu;function uc(){if(wu)return Ia;wu=1;var n=Ze();return Ia=Array.isArray||function(r){return n(r)==="Array"},Ia}var Pu;function lc(){if(Pu)return Cu;Pu=1;var n=le(),e=re(),r=uc(),t=e([].reverse),i=[1,2];return n({target:"Array",proto:!0,forced:String(i)===String(i.reverse())},{reverse:function(){return r(this)&&(this.length=this.length),t(this)}}),Cu}lc();/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Al=function(n,e){return(Al=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])})(n,e)};function Il(n,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=n}Al(n,e),n.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}function hc(n){var e="";Array.isArray(n)||(n=[n]);for(var r=0;r<n.length;r++){var t=n[r];if(t.type===S.CLOSE_PATH)e+="z";else if(t.type===S.HORIZ_LINE_TO)e+=(t.relative?"h":"H")+t.x;else if(t.type===S.VERT_LINE_TO)e+=(t.relative?"v":"V")+t.y;else if(t.type===S.MOVE_TO)e+=(t.relative?"m":"M")+t.x+" "+t.y;else if(t.type===S.LINE_TO)e+=(t.relative?"l":"L")+t.x+" "+t.y;else if(t.type===S.CURVE_TO)e+=(t.relative?"c":"C")+t.x1+" "+t.y1+" "+t.x2+" "+t.y2+" "+t.x+" "+t.y;else if(t.type===S.SMOOTH_CURVE_TO)e+=(t.relative?"s":"S")+t.x2+" "+t.y2+" "+t.x+" "+t.y;else if(t.type===S.QUAD_TO)e+=(t.relative?"q":"Q")+t.x1+" "+t.y1+" "+t.x+" "+t.y;else if(t.type===S.SMOOTH_QUAD_TO)e+=(t.relative?"t":"T")+t.x+" "+t.y;else{if(t.type!==S.ARC)throw new Error('Unexpected command type "'+t.type+'" at index '+r+".");e+=(t.relative?"a":"A")+t.rX+" "+t.rY+" "+t.xRot+" "+ +t.lArcFlag+" "+ +t.sweepFlag+" "+t.x+" "+t.y}}return e}function Da(n,e){var r=n[0],t=n[1];return[r*Math.cos(e)-t*Math.sin(e),r*Math.sin(e)+t*Math.cos(e)]}function Te(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];for(var r=0;r<n.length;r++)if(typeof n[r]!="number")throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof n[r]+" == typeof "+n[r]);return!0}var Ge=Math.PI;function Na(n,e,r){n.lArcFlag=n.lArcFlag===0?0:1,n.sweepFlag=n.sweepFlag===0?0:1;var t=n.rX,i=n.rY,a=n.x,s=n.y;t=Math.abs(n.rX),i=Math.abs(n.rY);var o=Da([(e-a)/2,(r-s)/2],-n.xRot/180*Ge),u=o[0],l=o[1],h=Math.pow(u,2)/Math.pow(t,2)+Math.pow(l,2)/Math.pow(i,2);1<h&&(t*=Math.sqrt(h),i*=Math.sqrt(h)),n.rX=t,n.rY=i;var v=Math.pow(t,2)*Math.pow(l,2)+Math.pow(i,2)*Math.pow(u,2),f=(n.lArcFlag!==n.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(t,2)*Math.pow(i,2)-v)/v)),c=t*l/i*f,g=-i*u/t*f,d=Da([c,g],n.xRot/180*Ge);n.cX=d[0]+(e+a)/2,n.cY=d[1]+(r+s)/2,n.phi1=Math.atan2((l-g)/i,(u-c)/t),n.phi2=Math.atan2((-l-g)/i,(-u-c)/t),n.sweepFlag===0&&n.phi2>n.phi1&&(n.phi2-=2*Ge),n.sweepFlag===1&&n.phi2<n.phi1&&(n.phi2+=2*Ge),n.phi1*=180/Ge,n.phi2*=180/Ge}function Au(n,e,r){Te(n,e,r);var t=n*n+e*e-r*r;if(0>t)return[];if(t===0)return[[n*r/(n*n+e*e),e*r/(n*n+e*e)]];var i=Math.sqrt(t);return[[(n*r+e*i)/(n*n+e*e),(e*r-n*i)/(n*n+e*e)],[(n*r-e*i)/(n*n+e*e),(e*r+n*i)/(n*n+e*e)]]}var ae,ke=Math.PI/180;function Iu(n,e,r){return(1-r)*n+r*e}function Nu(n,e,r,t){return n+Math.cos(t/180*Ge)*e+Math.sin(t/180*Ge)*r}function _u(n,e,r,t){var i=1e-6,a=e-n,s=r-e,o=3*a+3*(t-r)-6*s,u=6*(s-a),l=3*a;return Math.abs(o)<i?[-l/u]:function(h,v,f){var c=h*h/4-v;if(c<-f)return[];if(c<=f)return[-h/2];var g=Math.sqrt(c);return[-h/2-g,-h/2+g]}(u/o,l/o,i)}function Mu(n,e,r,t,i){var a=1-i;return n*(a*a*a)+e*(3*a*a*i)+r*(3*a*i*i)+t*(i*i*i)}(function(n){function e(){return i(function(o,u,l){return o.relative&&(o.x1!==void 0&&(o.x1+=u),o.y1!==void 0&&(o.y1+=l),o.x2!==void 0&&(o.x2+=u),o.y2!==void 0&&(o.y2+=l),o.x!==void 0&&(o.x+=u),o.y!==void 0&&(o.y+=l),o.relative=!1),o})}function r(){var o=NaN,u=NaN,l=NaN,h=NaN;return i(function(v,f,c){return v.type&S.SMOOTH_CURVE_TO&&(v.type=S.CURVE_TO,o=isNaN(o)?f:o,u=isNaN(u)?c:u,v.x1=v.relative?f-o:2*f-o,v.y1=v.relative?c-u:2*c-u),v.type&S.CURVE_TO?(o=v.relative?f+v.x2:v.x2,u=v.relative?c+v.y2:v.y2):(o=NaN,u=NaN),v.type&S.SMOOTH_QUAD_TO&&(v.type=S.QUAD_TO,l=isNaN(l)?f:l,h=isNaN(h)?c:h,v.x1=v.relative?f-l:2*f-l,v.y1=v.relative?c-h:2*c-h),v.type&S.QUAD_TO?(l=v.relative?f+v.x1:v.x1,h=v.relative?c+v.y1:v.y1):(l=NaN,h=NaN),v})}function t(){var o=NaN,u=NaN;return i(function(l,h,v){if(l.type&S.SMOOTH_QUAD_TO&&(l.type=S.QUAD_TO,o=isNaN(o)?h:o,u=isNaN(u)?v:u,l.x1=l.relative?h-o:2*h-o,l.y1=l.relative?v-u:2*v-u),l.type&S.QUAD_TO){o=l.relative?h+l.x1:l.x1,u=l.relative?v+l.y1:l.y1;var f=l.x1,c=l.y1;l.type=S.CURVE_TO,l.x1=((l.relative?0:h)+2*f)/3,l.y1=((l.relative?0:v)+2*c)/3,l.x2=(l.x+2*f)/3,l.y2=(l.y+2*c)/3}else o=NaN,u=NaN;return l})}function i(o){var u=0,l=0,h=NaN,v=NaN;return function(f){if(isNaN(h)&&!(f.type&S.MOVE_TO))throw new Error("path must start with moveto");var c=o(f,u,l,h,v);return f.type&S.CLOSE_PATH&&(u=h,l=v),f.x!==void 0&&(u=f.relative?u+f.x:f.x),f.y!==void 0&&(l=f.relative?l+f.y:f.y),f.type&S.MOVE_TO&&(h=u,v=l),c}}function a(o,u,l,h,v,f){return Te(o,u,l,h,v,f),i(function(c,g,d,p){var m=c.x1,y=c.x2,b=c.relative&&!isNaN(p),x=c.x!==void 0?c.x:b?0:g,T=c.y!==void 0?c.y:b?0:d;function E(H){return H*H}c.type&S.HORIZ_LINE_TO&&u!==0&&(c.type=S.LINE_TO,c.y=c.relative?0:d),c.type&S.VERT_LINE_TO&&l!==0&&(c.type=S.LINE_TO,c.x=c.relative?0:g),c.x!==void 0&&(c.x=c.x*o+T*l+(b?0:v)),c.y!==void 0&&(c.y=x*u+c.y*h+(b?0:f)),c.x1!==void 0&&(c.x1=c.x1*o+c.y1*l+(b?0:v)),c.y1!==void 0&&(c.y1=m*u+c.y1*h+(b?0:f)),c.x2!==void 0&&(c.x2=c.x2*o+c.y2*l+(b?0:v)),c.y2!==void 0&&(c.y2=y*u+c.y2*h+(b?0:f));var O=o*h-u*l;if(c.xRot!==void 0&&(o!==1||u!==0||l!==0||h!==1))if(O===0)delete c.rX,delete c.rY,delete c.xRot,delete c.lArcFlag,delete c.sweepFlag,c.type=S.LINE_TO;else{var _=c.xRot*Math.PI/180,V=Math.sin(_),P=Math.cos(_),M=1/E(c.rX),w=1/E(c.rY),L=E(P)*M+E(V)*w,R=2*V*P*(M-w),A=E(V)*M+E(P)*w,U=L*h*h-R*u*h+A*u*u,I=R*(o*h+u*l)-2*(L*l*h+A*o*u),j=L*l*l-R*o*l+A*o*o,N=(Math.atan2(I,U-j)+Math.PI)%Math.PI/2,k=Math.sin(N),B=Math.cos(N);c.rX=Math.abs(O)/Math.sqrt(U*E(B)+I*k*B+j*E(k)),c.rY=Math.abs(O)/Math.sqrt(U*E(k)-I*k*B+j*E(B)),c.xRot=180*N/Math.PI}return c.sweepFlag!==void 0&&0>O&&(c.sweepFlag=+!c.sweepFlag),c})}function s(){return function(o){var u={};for(var l in o)u[l]=o[l];return u}}n.ROUND=function(o){function u(l){return Math.round(l*o)/o}return o===void 0&&(o=1e13),Te(o),function(l){return l.x1!==void 0&&(l.x1=u(l.x1)),l.y1!==void 0&&(l.y1=u(l.y1)),l.x2!==void 0&&(l.x2=u(l.x2)),l.y2!==void 0&&(l.y2=u(l.y2)),l.x!==void 0&&(l.x=u(l.x)),l.y!==void 0&&(l.y=u(l.y)),l.rX!==void 0&&(l.rX=u(l.rX)),l.rY!==void 0&&(l.rY=u(l.rY)),l}},n.TO_ABS=e,n.TO_REL=function(){return i(function(o,u,l){return o.relative||(o.x1!==void 0&&(o.x1-=u),o.y1!==void 0&&(o.y1-=l),o.x2!==void 0&&(o.x2-=u),o.y2!==void 0&&(o.y2-=l),o.x!==void 0&&(o.x-=u),o.y!==void 0&&(o.y-=l),o.relative=!0),o})},n.NORMALIZE_HVZ=function(o,u,l){return o===void 0&&(o=!0),u===void 0&&(u=!0),l===void 0&&(l=!0),i(function(h,v,f,c,g){if(isNaN(c)&&!(h.type&S.MOVE_TO))throw new Error("path must start with moveto");return u&&h.type&S.HORIZ_LINE_TO&&(h.type=S.LINE_TO,h.y=h.relative?0:f),l&&h.type&S.VERT_LINE_TO&&(h.type=S.LINE_TO,h.x=h.relative?0:v),o&&h.type&S.CLOSE_PATH&&(h.type=S.LINE_TO,h.x=h.relative?c-v:c,h.y=h.relative?g-f:g),h.type&S.ARC&&(h.rX===0||h.rY===0)&&(h.type=S.LINE_TO,delete h.rX,delete h.rY,delete h.xRot,delete h.lArcFlag,delete h.sweepFlag),h})},n.NORMALIZE_ST=r,n.QT_TO_C=t,n.INFO=i,n.SANITIZE=function(o){o===void 0&&(o=0),Te(o);var u=NaN,l=NaN,h=NaN,v=NaN;return i(function(f,c,g,d,p){var m=Math.abs,y=!1,b=0,x=0;if(f.type&S.SMOOTH_CURVE_TO&&(b=isNaN(u)?0:c-u,x=isNaN(l)?0:g-l),f.type&(S.CURVE_TO|S.SMOOTH_CURVE_TO)?(u=f.relative?c+f.x2:f.x2,l=f.relative?g+f.y2:f.y2):(u=NaN,l=NaN),f.type&S.SMOOTH_QUAD_TO?(h=isNaN(h)?c:2*c-h,v=isNaN(v)?g:2*g-v):f.type&S.QUAD_TO?(h=f.relative?c+f.x1:f.x1,v=f.relative?g+f.y1:f.y2):(h=NaN,v=NaN),f.type&S.LINE_COMMANDS||f.type&S.ARC&&(f.rX===0||f.rY===0||!f.lArcFlag)||f.type&S.CURVE_TO||f.type&S.SMOOTH_CURVE_TO||f.type&S.QUAD_TO||f.type&S.SMOOTH_QUAD_TO){var T=f.x===void 0?0:f.relative?f.x:f.x-c,E=f.y===void 0?0:f.relative?f.y:f.y-g;b=isNaN(h)?f.x1===void 0?b:f.relative?f.x:f.x1-c:h-c,x=isNaN(v)?f.y1===void 0?x:f.relative?f.y:f.y1-g:v-g;var O=f.x2===void 0?0:f.relative?f.x:f.x2-c,_=f.y2===void 0?0:f.relative?f.y:f.y2-g;m(T)<=o&&m(E)<=o&&m(b)<=o&&m(x)<=o&&m(O)<=o&&m(_)<=o&&(y=!0)}return f.type&S.CLOSE_PATH&&m(c-d)<=o&&m(g-p)<=o&&(y=!0),y?[]:f})},n.MATRIX=a,n.ROTATE=function(o,u,l){u===void 0&&(u=0),l===void 0&&(l=0),Te(o,u,l);var h=Math.sin(o),v=Math.cos(o);return a(v,h,-h,v,u-u*v+l*h,l-u*h-l*v)},n.TRANSLATE=function(o,u){return u===void 0&&(u=0),Te(o,u),a(1,0,0,1,o,u)},n.SCALE=function(o,u){return u===void 0&&(u=o),Te(o,u),a(o,0,0,u,0,0)},n.SKEW_X=function(o){return Te(o),a(1,0,Math.atan(o),1,0,0)},n.SKEW_Y=function(o){return Te(o),a(1,Math.atan(o),0,1,0,0)},n.X_AXIS_SYMMETRY=function(o){return o===void 0&&(o=0),Te(o),a(-1,0,0,1,o,0)},n.Y_AXIS_SYMMETRY=function(o){return o===void 0&&(o=0),Te(o),a(1,0,0,-1,0,o)},n.A_TO_C=function(){return i(function(o,u,l){return S.ARC===o.type?function(h,v,f){var c,g,d,p;h.cX||Na(h,v,f);for(var m=Math.min(h.phi1,h.phi2),y=Math.max(h.phi1,h.phi2)-m,b=Math.ceil(y/90),x=new Array(b),T=v,E=f,O=0;O<b;O++){var _=Iu(h.phi1,h.phi2,O/b),V=Iu(h.phi1,h.phi2,(O+1)/b),P=V-_,M=4/3*Math.tan(P*ke/4),w=[Math.cos(_*ke)-M*Math.sin(_*ke),Math.sin(_*ke)+M*Math.cos(_*ke)],L=w[0],R=w[1],A=[Math.cos(V*ke),Math.sin(V*ke)],U=A[0],I=A[1],j=[U+M*Math.sin(V*ke),I-M*Math.cos(V*ke)],N=j[0],k=j[1];x[O]={relative:h.relative,type:S.CURVE_TO};var B=function(H,X){var $=Da([H*h.rX,X*h.rY],h.xRot),Q=$[0],Fe=$[1];return[h.cX+Q,h.cY+Fe]};c=B(L,R),x[O].x1=c[0],x[O].y1=c[1],g=B(N,k),x[O].x2=g[0],x[O].y2=g[1],d=B(U,I),x[O].x=d[0],x[O].y=d[1],h.relative&&(x[O].x1-=T,x[O].y1-=E,x[O].x2-=T,x[O].y2-=E,x[O].x-=T,x[O].y-=E),T=(p=[x[O].x,x[O].y])[0],E=p[1]}return x}(o,o.relative?0:u,o.relative?0:l):o})},n.ANNOTATE_ARCS=function(){return i(function(o,u,l){return o.relative&&(u=0,l=0),S.ARC===o.type&&Na(o,u,l),o})},n.CLONE=s,n.CALCULATE_BOUNDS=function(){var o=function(f){var c={};for(var g in f)c[g]=f[g];return c},u=e(),l=t(),h=r(),v=i(function(f,c,g){var d=h(l(u(o(f))));function p(k){k>v.maxX&&(v.maxX=k),k<v.minX&&(v.minX=k)}function m(k){k>v.maxY&&(v.maxY=k),k<v.minY&&(v.minY=k)}if(d.type&S.DRAWING_COMMANDS&&(p(c),m(g)),d.type&S.HORIZ_LINE_TO&&p(d.x),d.type&S.VERT_LINE_TO&&m(d.y),d.type&S.LINE_TO&&(p(d.x),m(d.y)),d.type&S.CURVE_TO){p(d.x),m(d.y);for(var y=0,b=_u(c,d.x1,d.x2,d.x);y<b.length;y++)0<(N=b[y])&&1>N&&p(Mu(c,d.x1,d.x2,d.x,N));for(var x=0,T=_u(g,d.y1,d.y2,d.y);x<T.length;x++)0<(N=T[x])&&1>N&&m(Mu(g,d.y1,d.y2,d.y,N))}if(d.type&S.ARC){p(d.x),m(d.y),Na(d,c,g);for(var E=d.xRot/180*Math.PI,O=Math.cos(E)*d.rX,_=Math.sin(E)*d.rX,V=-Math.sin(E)*d.rY,P=Math.cos(E)*d.rY,M=d.phi1<d.phi2?[d.phi1,d.phi2]:-180>d.phi2?[d.phi2+360,d.phi1+360]:[d.phi2,d.phi1],w=M[0],L=M[1],R=function(k){var B=k[0],H=k[1],X=180*Math.atan2(H,B)/Math.PI;return X<w?X+360:X},A=0,U=Au(V,-O,0).map(R);A<U.length;A++)(N=U[A])>w&&N<L&&p(Nu(d.cX,O,V,N));for(var I=0,j=Au(P,-_,0).map(R);I<j.length;I++){var N;(N=j[I])>w&&N<L&&m(Nu(d.cY,_,P,N))}}return f});return v.minX=1/0,v.maxX=-1/0,v.minY=1/0,v.maxY=-1/0,v}})(ae||(ae={}));var Se,Nl=function(){function n(){}return n.prototype.round=function(e){return this.transform(ae.ROUND(e))},n.prototype.toAbs=function(){return this.transform(ae.TO_ABS())},n.prototype.toRel=function(){return this.transform(ae.TO_REL())},n.prototype.normalizeHVZ=function(e,r,t){return this.transform(ae.NORMALIZE_HVZ(e,r,t))},n.prototype.normalizeST=function(){return this.transform(ae.NORMALIZE_ST())},n.prototype.qtToC=function(){return this.transform(ae.QT_TO_C())},n.prototype.aToC=function(){return this.transform(ae.A_TO_C())},n.prototype.sanitize=function(e){return this.transform(ae.SANITIZE(e))},n.prototype.translate=function(e,r){return this.transform(ae.TRANSLATE(e,r))},n.prototype.scale=function(e,r){return this.transform(ae.SCALE(e,r))},n.prototype.rotate=function(e,r,t){return this.transform(ae.ROTATE(e,r,t))},n.prototype.matrix=function(e,r,t,i,a,s){return this.transform(ae.MATRIX(e,r,t,i,a,s))},n.prototype.skewX=function(e){return this.transform(ae.SKEW_X(e))},n.prototype.skewY=function(e){return this.transform(ae.SKEW_Y(e))},n.prototype.xSymmetry=function(e){return this.transform(ae.X_AXIS_SYMMETRY(e))},n.prototype.ySymmetry=function(e){return this.transform(ae.Y_AXIS_SYMMETRY(e))},n.prototype.annotateArcs=function(){return this.transform(ae.ANNOTATE_ARCS())},n}(),cc=function(n){return n===" "||n==="	"||n==="\r"||n===`
`},qu=function(n){return 48<=n.charCodeAt(0)&&n.charCodeAt(0)<=57},vc=function(n){function e(){var r=n.call(this)||this;return r.curNumber="",r.curCommandType=-1,r.curCommandRelative=!1,r.canParseCommandOrComma=!0,r.curNumberHasExp=!1,r.curNumberHasExpDigits=!1,r.curNumberHasDecimal=!1,r.curArgs=[],r}return Il(e,n),e.prototype.finish=function(r){if(r===void 0&&(r=[]),this.parse(" ",r),this.curArgs.length!==0||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return r},e.prototype.parse=function(r,t){var i=this;t===void 0&&(t=[]);for(var a=function(v){t.push(v),i.curArgs.length=0,i.canParseCommandOrComma=!0},s=0;s<r.length;s++){var o=r[s],u=!(this.curCommandType!==S.ARC||this.curArgs.length!==3&&this.curArgs.length!==4||this.curNumber.length!==1||this.curNumber!=="0"&&this.curNumber!=="1"),l=qu(o)&&(this.curNumber==="0"&&o==="0"||u);if(!qu(o)||l)if(o!=="e"&&o!=="E")if(o!=="-"&&o!=="+"||!this.curNumberHasExp||this.curNumberHasExpDigits)if(o!=="."||this.curNumberHasExp||this.curNumberHasDecimal||u){if(this.curNumber&&this.curCommandType!==-1){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+s);if(this.curCommandType===S.ARC){if(this.curArgs.length===0||this.curArgs.length===1){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+s+'"')}else if((this.curArgs.length===3||this.curArgs.length===4)&&this.curNumber!=="0"&&this.curNumber!=="1")throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+s+'"')}this.curArgs.push(h),this.curArgs.length===fc[this.curCommandType]&&(S.HORIZ_LINE_TO===this.curCommandType?a({type:S.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):S.VERT_LINE_TO===this.curCommandType?a({type:S.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===S.MOVE_TO||this.curCommandType===S.LINE_TO||this.curCommandType===S.SMOOTH_QUAD_TO?(a({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),S.MOVE_TO===this.curCommandType&&(this.curCommandType=S.LINE_TO)):this.curCommandType===S.CURVE_TO?a({type:S.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===S.SMOOTH_CURVE_TO?a({type:S.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===S.QUAD_TO?a({type:S.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===S.ARC&&a({type:S.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!cc(o))if(o===","&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if(o!=="+"&&o!=="-"&&o!==".")if(l)this.curNumber=o,this.curNumberHasDecimal=!1;else{if(this.curArgs.length!==0)throw new SyntaxError("Unterminated command at index "+s+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+o+'" at index '+s+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,o!=="z"&&o!=="Z")if(o==="h"||o==="H")this.curCommandType=S.HORIZ_LINE_TO,this.curCommandRelative=o==="h";else if(o==="v"||o==="V")this.curCommandType=S.VERT_LINE_TO,this.curCommandRelative=o==="v";else if(o==="m"||o==="M")this.curCommandType=S.MOVE_TO,this.curCommandRelative=o==="m";else if(o==="l"||o==="L")this.curCommandType=S.LINE_TO,this.curCommandRelative=o==="l";else if(o==="c"||o==="C")this.curCommandType=S.CURVE_TO,this.curCommandRelative=o==="c";else if(o==="s"||o==="S")this.curCommandType=S.SMOOTH_CURVE_TO,this.curCommandRelative=o==="s";else if(o==="q"||o==="Q")this.curCommandType=S.QUAD_TO,this.curCommandRelative=o==="q";else if(o==="t"||o==="T")this.curCommandType=S.SMOOTH_QUAD_TO,this.curCommandRelative=o==="t";else{if(o!=="a"&&o!=="A")throw new SyntaxError('Unexpected character "'+o+'" at index '+s+".");this.curCommandType=S.ARC,this.curCommandRelative=o==="a"}else t.push({type:S.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=o,this.curNumberHasDecimal=o==="."}else this.curNumber+=o,this.curNumberHasDecimal=!0;else this.curNumber+=o;else this.curNumber+=o,this.curNumberHasExp=!0;else this.curNumber+=o,this.curNumberHasExpDigits=this.curNumberHasExp}return t},e.prototype.transform=function(r){return Object.create(this,{parse:{value:function(t,i){i===void 0&&(i=[]);for(var a=0,s=Object.getPrototypeOf(this).parse.call(this,t);a<s.length;a++){var o=s[a],u=r(o);Array.isArray(u)?i.push.apply(i,u):i.push(u)}return i}}})},e}(Nl),S=function(n){function e(r){var t=n.call(this)||this;return t.commands=typeof r=="string"?e.parse(r):r,t}return Il(e,n),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var r=ae.CALCULATE_BOUNDS();return this.transform(r),r},e.prototype.transform=function(r){for(var t=[],i=0,a=this.commands;i<a.length;i++){var s=r(a[i]);Array.isArray(s)?t.push.apply(t,s):t.push(s)}return this.commands=t,this},e.encode=function(r){return hc(r)},e.parse=function(r){var t=new vc,i=[];return t.parse(r,i),t.finish(i),i},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(Nl),fc=((Se={})[S.MOVE_TO]=2,Se[S.LINE_TO]=2,Se[S.HORIZ_LINE_TO]=1,Se[S.VERT_LINE_TO]=1,Se[S.CLOSE_PATH]=0,Se[S.QUAD_TO]=4,Se[S.SMOOTH_QUAD_TO]=2,Se[S.CURVE_TO]=6,Se[S.SMOOTH_CURVE_TO]=4,Se[S.ARC]=7,Se),Du={},_a,Vu;function gc(){if(Vu)return _a;Vu=1;var n=se(),e=we(),r=Ir(),t=Sl(),i=RegExp.prototype;return _a=function(a){var s=a.flags;return s===void 0&&!("flags"in i)&&!e(a,"flags")&&r(i,a)?n(t,a):s},_a}var ku;function dc(){if(ku)return Du;ku=1;var n=Vr().PROPER,e=rr(),r=ue(),t=qe(),i=ee(),a=gc(),s="toString",o=RegExp.prototype,u=o[s],l=i(function(){return u.call({source:"a",flags:"b"})!=="/a/b"}),h=n&&u.name!==s;return(l||h)&&e(o,s,function(){var f=r(this),c=t(f.source),g=t(a(f));return"/"+c+"/"+g},{unsafe:!0}),Du}dc();function Pr(n){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Pr=function(e){return typeof e}:Pr=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pr(n)}function pc(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}var yc=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],mc=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function bc(n,e,r,t,i){if(typeof n=="string"&&(n=document.getElementById(n)),!n||Pr(n)!=="object"||!("getContext"in n))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var a=n.getContext("2d");try{return a.getImageData(e,r,t,i)}catch(s){throw new Error("unable to access image data: "+s)}}function xc(n,e,r,t,i,a){if(!(isNaN(a)||a<1)){a|=0;var s=bc(n,e,r,t,i);s=Oc(s,e,r,t,i,a),n.getContext("2d").putImageData(s,e,r)}}function Oc(n,e,r,t,i,a){for(var s=n.data,o=2*a+1,u=t-1,l=i-1,h=a+1,v=h*(h+1)/2,f=new Lu,c=f,g,d=1;d<o;d++)c=c.next=new Lu,d===h&&(g=c);c.next=f;for(var p=null,m=null,y=0,b=0,x=yc[a],T=mc[a],E=0;E<i;E++){c=f;for(var O=s[b],_=s[b+1],V=s[b+2],P=s[b+3],M=0;M<h;M++)c.r=O,c.g=_,c.b=V,c.a=P,c=c.next;for(var w=0,L=0,R=0,A=0,U=h*O,I=h*_,j=h*V,N=h*P,k=v*O,B=v*_,H=v*V,X=v*P,$=1;$<h;$++){var Q=b+((u<$?u:$)<<2),Fe=s[Q],De=s[Q+1],Ae=s[Q+2],de=s[Q+3],pe=h-$;k+=(c.r=Fe)*pe,B+=(c.g=De)*pe,H+=(c.b=Ae)*pe,X+=(c.a=de)*pe,w+=Fe,L+=De,R+=Ae,A+=de,c=c.next}p=f,m=g;for(var ye=0;ye<t;ye++){var Ie=X*x>>>T;if(s[b+3]=Ie,Ie!==0){var Ne=255/Ie;s[b]=(k*x>>>T)*Ne,s[b+1]=(B*x>>>T)*Ne,s[b+2]=(H*x>>>T)*Ne}else s[b]=s[b+1]=s[b+2]=0;k-=U,B-=I,H-=j,X-=N,U-=p.r,I-=p.g,j-=p.b,N-=p.a;var he=ye+a+1;he=y+(he<u?he:u)<<2,w+=p.r=s[he],L+=p.g=s[he+1],R+=p.b=s[he+2],A+=p.a=s[he+3],k+=w,B+=L,H+=R,X+=A,p=p.next;var me=m,Ue=me.r,xr=me.g,or=me.b,Or=me.a;U+=Ue,I+=xr,j+=or,N+=Or,w-=Ue,L-=xr,R-=or,A-=Or,m=m.next,b+=4}y+=t}for(var fe=0;fe<t;fe++){b=fe<<2;var ce=s[b],be=s[b+1],C=s[b+2],q=s[b+3],G=h*ce,z=h*be,Y=h*C,_e=h*q,Ve=v*ce,xe=v*be,Oe=v*C,Ye=v*q;c=f;for(var ur=0;ur<h;ur++)c.r=ce,c.g=be,c.b=C,c.a=q,c=c.next;for(var Sr=t,$r=0,Yr=0,Wr=0,Xr=0,Tr=1;Tr<=a;Tr++){b=Sr+fe<<2;var Er=h-Tr;Ve+=(c.r=ce=s[b])*Er,xe+=(c.g=be=s[b+1])*Er,Oe+=(c.b=C=s[b+2])*Er,Ye+=(c.a=q=s[b+3])*Er,Xr+=ce,$r+=be,Yr+=C,Wr+=q,c=c.next,Tr<l&&(Sr+=t)}b=fe,p=f,m=g;for(var Kr=0;Kr<i;Kr++){var ve=b<<2;s[ve+3]=q=Ye*x>>>T,q>0?(q=255/q,s[ve]=(Ve*x>>>T)*q,s[ve+1]=(xe*x>>>T)*q,s[ve+2]=(Oe*x>>>T)*q):s[ve]=s[ve+1]=s[ve+2]=0,Ve-=G,xe-=z,Oe-=Y,Ye-=_e,G-=p.r,z-=p.g,Y-=p.b,_e-=p.a,ve=fe+((ve=Kr+h)<l?ve:l)*t<<2,Ve+=Xr+=p.r=s[ve],xe+=$r+=p.g=s[ve+1],Oe+=Yr+=p.b=s[ve+2],Ye+=Wr+=p.a=s[ve+3],p=p.next,G+=ce=m.r,z+=be=m.g,Y+=C=m.b,_e+=q=m.a,Xr-=ce,$r-=be,Yr-=C,Wr-=q,m=m.next,b+=t}}return n}var Lu=function n(){pc(this,n),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};function Sc(){var{DOMParser:n}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:n,createCanvas(r,t){return new OffscreenCanvas(r,t)},createImage(r){return je(function*(){var t=yield fetch(r),i=yield t.blob(),a=yield createImageBitmap(i);return a})()}};return(typeof DOMParser<"u"||typeof n>"u")&&Reflect.deleteProperty(e,"DOMParser"),e}function Tc(n){var{DOMParser:e,canvas:r,fetch:t}=n;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:t,createCanvas:r.createCanvas,createImage:r.loadImage}}var Dv=Object.freeze({__proto__:null,offscreen:Sc,node:Tc});function sr(n){return n.replace(/(?!\u3000)\s+/gm," ")}function Ec(n){return n.replace(/^[\n \t]+/,"")}function Rc(n){return n.replace(/[\n \t]+$/,"")}function ge(n){var e=(n||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[];return e.map(parseFloat)}var Cc=/^[A-Z-]+$/;function wc(n){return Cc.test(n)?n.toLowerCase():n}function _l(n){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(n)||[];return e[2]||e[3]||e[4]}function Pc(n){if(!n.startsWith("rgb"))return n;var e=3,r=n.replace(/\d+(\.\d+)?/g,(t,i)=>e--&&i?String(Math.round(parseFloat(t))):t);return r}var Ac=/(\[[^\]]+\])/g,Ic=/(#[^\s+>~.[:]+)/g,Nc=/(\.[^\s+>~.[:]+)/g,_c=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,Mc=/(:[\w-]+\([^)]*\))/gi,qc=/(:[^\s+>~.[:]+)/g,Dc=/([^\s+>~.[:]+)/g;function We(n,e){var r=e.exec(n);return r?[n.replace(e," "),r.length]:[n,0]}function Vc(n){var e=[0,0,0],r=n.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),t=0;return[r,t]=We(r,Ac),e[1]+=t,[r,t]=We(r,Ic),e[0]+=t,[r,t]=We(r,Nc),e[1]+=t,[r,t]=We(r,_c),e[2]+=t,[r,t]=We(r,Mc),e[1]+=t,[r,t]=We(r,qc),e[1]+=t,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,t]=We(r,Dc),e[2]+=t,e.join("")}var ir=1e-8;function ju(n){return Math.sqrt(Math.pow(n[0],2)+Math.pow(n[1],2))}function Va(n,e){return(n[0]*e[0]+n[1]*e[1])/(ju(n)*ju(e))}function Bu(n,e){return(n[0]*e[1]<n[1]*e[0]?-1:1)*Math.acos(Va(n,e))}function Fu(n){return n*n*n}function Uu(n){return 3*n*n*(1-n)}function Gu(n){return 3*n*(1-n)*(1-n)}function zu(n){return(1-n)*(1-n)*(1-n)}function Hu(n){return n*n}function $u(n){return 2*n*(1-n)}function Yu(n){return(1-n)*(1-n)}class D{constructor(e,r,t){this.document=e,this.name=r,this.value=t,this.isNormalizedColor=!1}static empty(e){return new D(e,"EMPTY","")}split(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:" ",{document:r,name:t}=this;return sr(this.getString()).trim().split(e).map(i=>new D(r,t,i))}hasValue(e){var{value:r}=this;return r!==null&&r!==""&&(e||r!==0)&&typeof r<"u"}isString(e){var{value:r}=this,t=typeof r=="string";return!t||!e?t:e.test(r)}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var e=this.getString();switch(!0){case e.endsWith("px"):case/^[0-9]+$/.test(e):return!0;default:return!1}}setValue(e){return this.value=e,this}getValue(e){return typeof e>"u"||this.hasValue()?this.value:e}getNumber(e){if(!this.hasValue())return typeof e>"u"?0:parseFloat(e);var{value:r}=this,t=parseFloat(r);return this.isString(/%$/)&&(t/=100),t}getString(e){return typeof e>"u"||this.hasValue()?typeof this.value>"u"?"":String(this.value):String(e)}getColor(e){var r=this.getString(e);return this.isNormalizedColor||(this.isNormalizedColor=!0,r=Pc(r),this.value=r),r}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!this.hasValue())return 0;var[t,i]=typeof e=="boolean"?[void 0,e]:[e],{viewPort:a}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(a.computeSize("x"),a.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(a.computeSize("x"),a.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*a.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*a.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return this.getNumber()*15;case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case(this.isString(/%$/)&&i):return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*a.computeSize(t);default:{var s=this.getNumber();return r&&s<1?s*a.computeSize(t):s}}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():this.getNumber()*1e3:0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var e=this.getString(),r=/#([^)'"]+)/.exec(e);return r&&(r=r[1]),r||(r=e),this.document.definitions[r]}getFillStyleDefinition(e,r){var t=this.getDefinition();if(!t)return null;if(typeof t.createGradient=="function")return t.createGradient(this.document.ctx,e,r);if(typeof t.createPattern=="function"){if(t.getHrefAttribute().hasValue()){var i=t.getAttribute("patternTransform");t=t.getHrefAttribute().getDefinition(),i.hasValue()&&t.getAttribute("patternTransform",!0).setValue(i.value)}return t.createPattern(this.document.ctx,e,r)}return null}getTextBaseline(){return this.hasValue()?D.textBaselineMapping[this.getString()]:null}addOpacity(e){for(var r=this.getColor(),t=r.length,i=0,a=0;a<t&&(r[a]===","&&i++,i!==3);a++);if(e.hasValue()&&this.isString()&&i!==3){var s=new qa(r);s.ok&&(s.alpha=e.getNumber(),r=s.toRGBA())}return new D(this.document,this.name,r)}}D.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class kc{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(e,r){this.viewPorts.push({width:e,height:r})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:e}=this;return e[e.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(e){return typeof e=="number"?e:e==="x"?this.width:e==="y"?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class Z{constructor(e,r){this.x=e,this.y=r}static parse(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,[t=r,i=r]=ge(e);return new Z(t,i)}static parseScale(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,[t=r,i=t]=ge(e);return new Z(t,i)}static parsePath(e){for(var r=ge(e),t=r.length,i=[],a=0;a<t;a+=2)i.push(new Z(r[a],r[a+1]));return i}angleTo(e){return Math.atan2(e.y-this.y,e.x-this.x)}applyTransform(e){var{x:r,y:t}=this,i=r*e[0]+t*e[2]+e[4],a=r*e[1]+t*e[3]+e[5];this.x=i,this.y=a}}class Lc{constructor(e){this.screen=e,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:e,onClick:r,onMouseMove:t}=this,i=e.ctx.canvas;i.onclick=r,i.onmousemove=t,this.working=!0}}stop(){if(this.working){var e=this.screen.ctx.canvas;this.working=!1,e.onclick=null,e.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:e,events:r,eventElements:t}=this,{style:i}=e.ctx.canvas;i&&(i.cursor=""),r.forEach((a,s)=>{for(var{run:o}=a,u=t[s];u;)o(u),u=u.parent}),this.events=[],this.eventElements=[]}}checkPath(e,r){if(!(!this.working||!r)){var{events:t,eventElements:i}=this;t.forEach((a,s)=>{var{x:o,y:u}=a;!i[s]&&r.isPointInPath&&r.isPointInPath(o,u)&&(i[s]=e)})}}checkBoundingBox(e,r){if(!(!this.working||!r)){var{events:t,eventElements:i}=this;t.forEach((a,s)=>{var{x:o,y:u}=a;!i[s]&&r.isPointInBox(o,u)&&(i[s]=e)})}}mapXY(e,r){for(var{window:t,ctx:i}=this.screen,a=new Z(e,r),s=i.canvas;s;)a.x-=s.offsetLeft,a.y-=s.offsetTop,s=s.offsetParent;return t.scrollX&&(a.x+=t.scrollX),t.scrollY&&(a.y+=t.scrollY),a}onClick(e){var{x:r,y:t}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onclick",x:r,y:t,run(i){i.onClick&&i.onClick()}})}onMouseMove(e){var{x:r,y:t}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onmousemove",x:r,y:t,run(i){i.onMouseMove&&i.onMouseMove()}})}}var Ml=typeof window<"u"?window:null,ql=typeof fetch<"u"?fetch.bind(void 0):null;class Ur{constructor(e){var{fetch:r=ql,window:t=Ml}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.ctx=e,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new kc,this.mouse=new Lc(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=t,this.fetch=r}wait(e){this.waits.push(e)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var e=this.waits.every(r=>r());return e&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=e,e}setDefaults(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4}setViewBox(e){var{document:r,ctx:t,aspectRatio:i,width:a,desiredWidth:s,height:o,desiredHeight:u,minX:l=0,minY:h=0,refX:v,refY:f,clip:c=!1,clipX:g=0,clipY:d=0}=e,p=sr(i).replace(/^defer\s/,""),[m,y]=p.split(" "),b=m||"xMidYMid",x=y||"meet",T=a/s,E=o/u,O=Math.min(T,E),_=Math.max(T,E),V=s,P=u;x==="meet"&&(V*=O,P*=O),x==="slice"&&(V*=_,P*=_);var M=new D(r,"refX",v),w=new D(r,"refY",f),L=M.hasValue()&&w.hasValue();if(L&&t.translate(-O*M.getPixels("x"),-O*w.getPixels("y")),c){var R=O*g,A=O*d;t.beginPath(),t.moveTo(R,A),t.lineTo(a,A),t.lineTo(a,o),t.lineTo(R,o),t.closePath(),t.clip()}if(!L){var U=x==="meet"&&O===E,I=x==="slice"&&_===E,j=x==="meet"&&O===T,N=x==="slice"&&_===T;b.startsWith("xMid")&&(U||I)&&t.translate(a/2-V/2,0),b.endsWith("YMid")&&(j||N)&&t.translate(0,o/2-P/2),b.startsWith("xMax")&&(U||I)&&t.translate(a-V,0),b.endsWith("YMax")&&(j||N)&&t.translate(0,o-P)}switch(!0){case b==="none":t.scale(T,E);break;case x==="meet":t.scale(O,O);break;case x==="slice":t.scale(_,_);break}t.translate(-l,-h)}start(e){var{enableRedraw:r=!1,ignoreMouse:t=!1,ignoreAnimation:i=!1,ignoreDimensions:a=!1,ignoreClear:s=!1,forceRedraw:o,scaleWidth:u,scaleHeight:l,offsetX:h,offsetY:v}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{FRAMERATE:f,mouse:c}=this,g=1e3/f;if(this.frameDuration=g,this.readyPromise=new Promise(b=>{this.resolveReady=b}),this.isReady()&&this.render(e,a,s,u,l,h,v),!!r){var d=Date.now(),p=d,m=0,y=()=>{d=Date.now(),m=d-p,m>=g&&(p=d-m%g,this.shouldUpdate(i,o)&&(this.render(e,a,s,u,l,h,v),c.runEvents())),this.intervalId=Ra(y)};t||c.start(),this.intervalId=Ra(y)}}stop(){this.intervalId&&(Ra.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(e,r){if(!e){var{frameDuration:t}=this,i=this.animations.reduce((a,s)=>s.update(t)||a,!1);if(i)return!0}return!!(typeof r=="function"&&r()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(e,r,t,i,a,s,o){var{CLIENT_WIDTH:u,CLIENT_HEIGHT:l,viewPort:h,ctx:v,isFirstRender:f}=this,c=v.canvas;h.clear(),c.width&&c.height?h.setCurrent(c.width,c.height):h.setCurrent(u,l);var g=e.getStyle("width"),d=e.getStyle("height");!r&&(f||typeof i!="number"&&typeof a!="number")&&(g.hasValue()&&(c.width=g.getPixels("x"),c.style&&(c.style.width="".concat(c.width,"px"))),d.hasValue()&&(c.height=d.getPixels("y"),c.style&&(c.style.height="".concat(c.height,"px"))));var p=c.clientWidth||c.width,m=c.clientHeight||c.height;if(r&&g.hasValue()&&d.hasValue()&&(p=g.getPixels("x"),m=d.getPixels("y")),h.setCurrent(p,m),typeof s=="number"&&e.getAttribute("x",!0).setValue(s),typeof o=="number"&&e.getAttribute("y",!0).setValue(o),typeof i=="number"||typeof a=="number"){var y=ge(e.getAttribute("viewBox").getString()),b=0,x=0;if(typeof i=="number"){var T=e.getStyle("width");T.hasValue()?b=T.getPixels("x")/i:isNaN(y[2])||(b=y[2]/i)}if(typeof a=="number"){var E=e.getStyle("height");E.hasValue()?x=E.getPixels("y")/a:isNaN(y[3])||(x=y[3]/a)}b||(b=x),x||(x=b),e.getAttribute("width",!0).setValue(i),e.getAttribute("height",!0).setValue(a);var O=e.getStyle("transform",!0,!0);O.setValue("".concat(O.getString()," scale(").concat(1/b,", ").concat(1/x,")"))}t||v.clearRect(0,0,p,m),e.render(v),f&&(this.isFirstRender=!1)}}Ur.defaultWindow=Ml;Ur.defaultFetch=ql;var{defaultFetch:jc}=Ur,Bc=typeof DOMParser<"u"?DOMParser:null;class Ma{constructor(){var{fetch:e=jc,DOMParser:r=Bc}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fetch=e,this.DOMParser=r}parse(e){var r=this;return je(function*(){return e.startsWith("<")?r.parseFromString(e):r.load(e)})()}parseFromString(e){var r=new this.DOMParser;try{return this.checkDocument(r.parseFromString(e,"image/svg+xml"))}catch{return this.checkDocument(r.parseFromString(e,"text/xml"))}}checkDocument(e){var r=e.getElementsByTagName("parsererror")[0];if(r)throw new Error(r.textContent);return e}load(e){var r=this;return je(function*(){var t=yield r.fetch(e),i=yield t.text();return r.parseFromString(i)})()}}class Fc{constructor(e,r){this.type="translate",this.point=null,this.point=Z.parse(r)}apply(e){var{x:r,y:t}=this.point;e.translate(r||0,t||0)}unapply(e){var{x:r,y:t}=this.point;e.translate(-1*r||0,-1*t||0)}applyToPoint(e){var{x:r,y:t}=this.point;e.applyTransform([1,0,0,1,r||0,t||0])}}class Uc{constructor(e,r,t){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=ge(r);this.angle=new D(e,"angle",i[0]),this.originX=t[0],this.originY=t[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(e){var{cx:r,cy:t,originX:i,originY:a,angle:s}=this,o=r+i.getPixels("x"),u=t+a.getPixels("y");e.translate(o,u),e.rotate(s.getRadians()),e.translate(-o,-u)}unapply(e){var{cx:r,cy:t,originX:i,originY:a,angle:s}=this,o=r+i.getPixels("x"),u=t+a.getPixels("y");e.translate(o,u),e.rotate(-1*s.getRadians()),e.translate(-o,-u)}applyToPoint(e){var{cx:r,cy:t,angle:i}=this,a=i.getRadians();e.applyTransform([1,0,0,1,r||0,t||0]),e.applyTransform([Math.cos(a),Math.sin(a),-Math.sin(a),Math.cos(a),0,0]),e.applyTransform([1,0,0,1,-r||0,-t||0])}}class Gc{constructor(e,r,t){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=Z.parseScale(r);(i.x===0||i.y===0)&&(i.x=ir,i.y=ir),this.scale=i,this.originX=t[0],this.originY=t[1]}apply(e){var{scale:{x:r,y:t},originX:i,originY:a}=this,s=i.getPixels("x"),o=a.getPixels("y");e.translate(s,o),e.scale(r,t||r),e.translate(-s,-o)}unapply(e){var{scale:{x:r,y:t},originX:i,originY:a}=this,s=i.getPixels("x"),o=a.getPixels("y");e.translate(s,o),e.scale(1/r,1/t||r),e.translate(-s,-o)}applyToPoint(e){var{x:r,y:t}=this.scale;e.applyTransform([r||0,0,0,t||0,0,0])}}class Dl{constructor(e,r,t){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=ge(r),this.originX=t[0],this.originY=t[1]}apply(e){var{originX:r,originY:t,matrix:i}=this,a=r.getPixels("x"),s=t.getPixels("y");e.translate(a,s),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),e.translate(-a,-s)}unapply(e){var{originX:r,originY:t,matrix:i}=this,a=i[0],s=i[2],o=i[4],u=i[1],l=i[3],h=i[5],v=0,f=0,c=1,g=1/(a*(l*c-h*f)-s*(u*c-h*v)+o*(u*f-l*v)),d=r.getPixels("x"),p=t.getPixels("y");e.translate(d,p),e.transform(g*(l*c-h*f),g*(h*v-u*c),g*(o*f-s*c),g*(a*c-o*v),g*(s*h-o*l),g*(o*u-a*h)),e.translate(-d,-p)}applyToPoint(e){e.applyTransform(this.matrix)}}class Vl extends Dl{constructor(e,r,t){super(e,r,t),this.type="skew",this.angle=null,this.angle=new D(e,"angle",r)}}class zc extends Vl{constructor(e,r,t){super(e,r,t),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class Hc extends Vl{constructor(e,r,t){super(e,r,t),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}function $c(n){return sr(n).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}function Yc(n){var[e,r]=n.split("(");return[e.trim(),r.trim().replace(")","")]}class Qe{constructor(e,r,t){this.document=e,this.transforms=[];var i=$c(r);i.forEach(a=>{if(a!=="none"){var[s,o]=Yc(a),u=Qe.transformTypes[s];typeof u<"u"&&this.transforms.push(new u(this.document,o,t))}})}static fromElement(e,r){var t=r.getStyle("transform",!1,!0),[i,a=i]=r.getStyle("transform-origin",!1,!0).split(),s=[i,a];return t.hasValue()?new Qe(e,t.getString(),s):null}apply(e){for(var{transforms:r}=this,t=r.length,i=0;i<t;i++)r[i].apply(e)}unapply(e){for(var{transforms:r}=this,t=r.length,i=t-1;i>=0;i--)r[i].unapply(e)}applyToPoint(e){for(var{transforms:r}=this,t=r.length,i=0;i<t;i++)r[i].applyToPoint(e)}}Qe.transformTypes={translate:Fc,rotate:Uc,scale:Gc,matrix:Dl,skewX:zc,skewY:Hc};class K{constructor(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this.document=e,this.node=r,this.captureTextNodes=t,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!(!r||r.nodeType!==1)){if(Array.from(r.attributes).forEach(o=>{var u=wc(o.nodeName);this.attributes[u]=new D(e,u,o.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var i=this.getAttribute("style").getString().split(";").map(o=>o.trim());i.forEach(o=>{if(o){var[u,l]=o.split(":").map(h=>h.trim());this.styles[u]=new D(e,u,l)}})}var{definitions:a}=e,s=this.getAttribute("id");s.hasValue()&&(a[s.getString()]||(a[s.getString()]=this)),Array.from(r.childNodes).forEach(o=>{if(o.nodeType===1)this.addChild(o);else if(t&&(o.nodeType===3||o.nodeType===4)){var u=e.createTextNode(o);u.getText().length>0&&this.addChild(u)}})}}getAttribute(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=this.attributes[e];if(!t&&r){var i=new D(this.document,e,"");return this.attributes[e]=i,i}return t||D.empty(this.document)}getHrefAttribute(){for(var e in this.attributes)if(e==="href"||e.endsWith(":href"))return this.attributes[e];return D.empty(this.document)}getStyle(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=this.styles[e];if(i)return i;var a=this.getAttribute(e);if(a!=null&&a.hasValue())return this.styles[e]=a,a;if(!t){var{parent:s}=this;if(s){var o=s.getStyle(e);if(o!=null&&o.hasValue())return o}}if(r){var u=new D(this.document,e,"");return this.styles[e]=u,u}return i||D.empty(this.document)}render(e){if(!(this.getStyle("display").getString()==="none"||this.getStyle("visibility").getString()==="hidden")){if(e.save(),this.getStyle("mask").hasValue()){var r=this.getStyle("mask").getDefinition();r&&(this.applyEffects(e),r.apply(e,this))}else if(this.getStyle("filter").getValue("none")!=="none"){var t=this.getStyle("filter").getDefinition();t&&(this.applyEffects(e),t.apply(e,this))}else this.setContext(e),this.renderChildren(e),this.clearContext(e);e.restore()}}setContext(e){}applyEffects(e){var r=Qe.fromElement(this.document,this);r&&r.apply(e);var t=this.getStyle("clip-path",!1,!0);if(t.hasValue()){var i=t.getDefinition();i&&i.apply(e)}}clearContext(e){}renderChildren(e){this.children.forEach(r=>{r.render(e)})}addChild(e){var r=e instanceof K?e:this.document.createElement(e);r.parent=this,K.ignoreChildTypes.includes(r.type)||this.children.push(r)}matchesSelector(e){var r,{node:t}=this;if(typeof t.matches=="function")return t.matches(e);var i=(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"class");return!i||i===""?!1:i.split(" ").some(a=>".".concat(a)===e)}addStylesFromStyleDefinition(){var{styles:e,stylesSpecificity:r}=this.document;for(var t in e)if(!t.startsWith("@")&&this.matchesSelector(t)){var i=e[t],a=r[t];if(i)for(var s in i){var o=this.stylesSpecificity[s];typeof o>"u"&&(o="000"),a>=o&&(this.styles[s]=i[s],this.stylesSpecificity[s]=a)}}}removeStyles(e,r){var t=r.reduce((i,a)=>{var s=e.getStyle(a);if(!s.hasValue())return i;var o=s.getString();return s.setValue(""),[...i,[a,o]]},[]);return t}restoreStyles(e,r){r.forEach(t=>{var[i,a]=t;e.getStyle(i,!0).setValue(a)})}isFirstChild(){var e;return((e=this.parent)===null||e===void 0?void 0:e.children.indexOf(this))===0}}K.ignoreChildTypes=["title"];class Wc extends K{constructor(e,r,t){super(e,r,t)}}function Xc(n){var e=n.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function Kc(n){return typeof process>"u"?n:n.trim().split(",").map(Xc).join(",")}function Qc(n){if(!n)return"";var e=n.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function Zc(n){if(!n)return"";var e=n.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class oe{constructor(e,r,t,i,a,s){var o=s?typeof s=="string"?oe.parse(s):s:{};this.fontFamily=a||o.fontFamily,this.fontSize=i||o.fontSize,this.fontStyle=e||o.fontStyle,this.fontWeight=t||o.fontWeight,this.fontVariant=r||o.fontVariant}static parse(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1?arguments[1]:void 0,t="",i="",a="",s="",o="",u=sr(e).trim().split(" "),l={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return u.forEach(h=>{switch(!0){case(!l.fontStyle&&oe.styles.includes(h)):h!=="inherit"&&(t=h),l.fontStyle=!0;break;case(!l.fontVariant&&oe.variants.includes(h)):h!=="inherit"&&(i=h),l.fontStyle=!0,l.fontVariant=!0;break;case(!l.fontWeight&&oe.weights.includes(h)):h!=="inherit"&&(a=h),l.fontStyle=!0,l.fontVariant=!0,l.fontWeight=!0;break;case!l.fontSize:h!=="inherit"&&([s]=h.split("/")),l.fontStyle=!0,l.fontVariant=!0,l.fontWeight=!0,l.fontSize=!0;break;default:h!=="inherit"&&(o+=h)}}),new oe(t,i,a,s,o,r)}toString(){return[Qc(this.fontStyle),this.fontVariant,Zc(this.fontWeight),this.fontSize,Kc(this.fontFamily)].join(" ").trim()}}oe.styles="normal|italic|oblique|inherit";oe.variants="normal|small-caps|inherit";oe.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class Pe{constructor(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Number.NaN,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.NaN,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Number.NaN,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Number.NaN;this.x1=e,this.y1=r,this.x2=t,this.y2=i,this.addPoint(e,r),this.addPoint(t,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(e,r){typeof e<"u"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),typeof r<"u"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=r,this.y2=r),r<this.y1&&(this.y1=r),r>this.y2&&(this.y2=r))}addX(e){this.addPoint(e,null)}addY(e){this.addPoint(null,e)}addBoundingBox(e){if(e){var{x1:r,y1:t,x2:i,y2:a}=e;this.addPoint(r,t),this.addPoint(i,a)}}sumCubic(e,r,t,i,a){return Math.pow(1-e,3)*r+3*Math.pow(1-e,2)*e*t+3*(1-e)*Math.pow(e,2)*i+Math.pow(e,3)*a}bezierCurveAdd(e,r,t,i,a){var s=6*r-12*t+6*i,o=-3*r+9*t-9*i+3*a,u=3*t-3*r;if(o===0){if(s===0)return;var l=-u/s;0<l&&l<1&&(e?this.addX(this.sumCubic(l,r,t,i,a)):this.addY(this.sumCubic(l,r,t,i,a)));return}var h=Math.pow(s,2)-4*u*o;if(!(h<0)){var v=(-s+Math.sqrt(h))/(2*o);0<v&&v<1&&(e?this.addX(this.sumCubic(v,r,t,i,a)):this.addY(this.sumCubic(v,r,t,i,a)));var f=(-s-Math.sqrt(h))/(2*o);0<f&&f<1&&(e?this.addX(this.sumCubic(f,r,t,i,a)):this.addY(this.sumCubic(f,r,t,i,a)))}}addBezierCurve(e,r,t,i,a,s,o,u){this.addPoint(e,r),this.addPoint(o,u),this.bezierCurveAdd(!0,e,t,a,o),this.bezierCurveAdd(!1,r,i,s,u)}addQuadraticCurve(e,r,t,i,a,s){var o=e+.6666666666666666*(t-e),u=r+2/3*(i-r),l=o+1/3*(a-e),h=u+1/3*(s-r);this.addBezierCurve(e,r,o,l,u,h,a,s)}isPointInBox(e,r){var{x1:t,y1:i,x2:a,y2:s}=this;return t<=e&&e<=a&&i<=r&&r<=s}}class F extends S{constructor(e){super(e.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new Z(0,0),this.control=new Z(0,0),this.current=new Z(0,0),this.points=[],this.angles=[]}isEnd(){var{i:e,commands:r}=this;return e>=r.length-1}next(){var e=this.commands[++this.i];return this.previousCommand=this.command,this.command=e,e}getPoint(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"x",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"y",t=new Z(this.command[e],this.command[r]);return this.makeAbsolute(t)}getAsControlPoint(e,r){var t=this.getPoint(e,r);return this.control=t,t}getAsCurrentPoint(e,r){var t=this.getPoint(e,r);return this.current=t,t}getReflectedControlPoint(){var e=this.previousCommand.type;if(e!==S.CURVE_TO&&e!==S.SMOOTH_CURVE_TO&&e!==S.QUAD_TO&&e!==S.SMOOTH_QUAD_TO)return this.current;var{current:{x:r,y:t},control:{x:i,y:a}}=this,s=new Z(2*r-i,2*t-a);return s}makeAbsolute(e){if(this.command.relative){var{x:r,y:t}=this.current;e.x+=r,e.y+=t}return e}addMarker(e,r,t){var{points:i,angles:a}=this;t&&a.length>0&&!a[a.length-1]&&(a[a.length-1]=i[i.length-1].angleTo(t)),this.addMarkerAngle(e,r?r.angleTo(e):null)}addMarkerAngle(e,r){this.points.push(e),this.angles.push(r)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:e}=this,r=e.length,t=0;t<r;t++)if(!e[t]){for(var i=t+1;i<r;i++)if(e[i]){e[t]=e[i];break}}return e}}class tr extends K{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var e=1,r=this;r;){var t=r.getStyle("opacity",!1,!0);t.hasValue(!0)&&(e*=t.getNumber()),r=r.parent}return e}setContext(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!r){var t=this.getStyle("fill"),i=this.getStyle("fill-opacity"),a=this.getStyle("stroke"),s=this.getStyle("stroke-opacity");if(t.isUrlDefinition()){var o=t.getFillStyleDefinition(this,i);o&&(e.fillStyle=o)}else if(t.hasValue()){t.getString()==="currentColor"&&t.setValue(this.getStyle("color").getColor());var u=t.getColor();u!=="inherit"&&(e.fillStyle=u==="none"?"rgba(0,0,0,0)":u)}if(i.hasValue()){var l=new D(this.document,"fill",e.fillStyle).addOpacity(i).getColor();e.fillStyle=l}if(a.isUrlDefinition()){var h=a.getFillStyleDefinition(this,s);h&&(e.strokeStyle=h)}else if(a.hasValue()){a.getString()==="currentColor"&&a.setValue(this.getStyle("color").getColor());var v=a.getString();v!=="inherit"&&(e.strokeStyle=v==="none"?"rgba(0,0,0,0)":v)}if(s.hasValue()){var f=new D(this.document,"stroke",e.strokeStyle).addOpacity(s).getString();e.strokeStyle=f}var c=this.getStyle("stroke-width");if(c.hasValue()){var g=c.getPixels();e.lineWidth=g||ir}var d=this.getStyle("stroke-linecap"),p=this.getStyle("stroke-linejoin"),m=this.getStyle("stroke-miterlimit"),y=this.getStyle("stroke-dasharray"),b=this.getStyle("stroke-dashoffset");if(d.hasValue()&&(e.lineCap=d.getString()),p.hasValue()&&(e.lineJoin=p.getString()),m.hasValue()&&(e.miterLimit=m.getNumber()),y.hasValue()&&y.getString()!=="none"){var x=ge(y.getString());typeof e.setLineDash<"u"?e.setLineDash(x):typeof e.webkitLineDash<"u"?e.webkitLineDash=x:typeof e.mozDash<"u"&&!(x.length===1&&x[0]===0)&&(e.mozDash=x);var T=b.getPixels();typeof e.lineDashOffset<"u"?e.lineDashOffset=T:typeof e.webkitLineDashOffset<"u"?e.webkitLineDashOffset=T:typeof e.mozDashOffset<"u"&&(e.mozDashOffset=T)}}if(this.modifiedEmSizeStack=!1,typeof e.font<"u"){var E=this.getStyle("font"),O=this.getStyle("font-style"),_=this.getStyle("font-variant"),V=this.getStyle("font-weight"),P=this.getStyle("font-size"),M=this.getStyle("font-family"),w=new oe(O.getString(),_.getString(),V.getString(),P.hasValue()?"".concat(P.getPixels(!0),"px"):"",M.getString(),oe.parse(E.getString(),e.font));O.setValue(w.fontStyle),_.setValue(w.fontVariant),V.setValue(w.fontWeight),P.setValue(w.fontSize),M.setValue(w.fontFamily),e.font=w.toString(),P.isPixels()&&(this.document.emSize=P.getPixels(),this.modifiedEmSizeStack=!0)}r||(this.applyEffects(e),e.globalAlpha=this.calculateOpacity())}clearContext(e){super.clearContext(e),this.modifiedEmSizeStack&&this.document.popEmSize()}}class W extends tr{constructor(e,r,t){super(e,r,t),this.type="path",this.pathParser=null,this.pathParser=new F(this.getAttribute("d").getString())}path(e){var{pathParser:r}=this,t=new Pe;for(r.reset(),e&&e.beginPath();!r.isEnd();)switch(r.next().type){case F.MOVE_TO:this.pathM(e,t);break;case F.LINE_TO:this.pathL(e,t);break;case F.HORIZ_LINE_TO:this.pathH(e,t);break;case F.VERT_LINE_TO:this.pathV(e,t);break;case F.CURVE_TO:this.pathC(e,t);break;case F.SMOOTH_CURVE_TO:this.pathS(e,t);break;case F.QUAD_TO:this.pathQ(e,t);break;case F.SMOOTH_QUAD_TO:this.pathT(e,t);break;case F.ARC:this.pathA(e,t);break;case F.CLOSE_PATH:this.pathZ(e,t);break}return t}getBoundingBox(e){return this.path()}getMarkers(){var{pathParser:e}=this,r=e.getMarkerPoints(),t=e.getMarkerAngles(),i=r.map((a,s)=>[a,t[s]]);return i}renderChildren(e){this.path(e),this.document.screen.mouse.checkPath(this,e);var r=this.getStyle("fill-rule");e.fillStyle!==""&&(r.getString("inherit")!=="inherit"?e.fill(r.getString()):e.fill()),e.strokeStyle!==""&&(this.getAttribute("vector-effect").getString()==="non-scaling-stroke"?(e.save(),e.setTransform(1,0,0,1,0,0),e.stroke(),e.restore()):e.stroke());var t=this.getMarkers();if(t){var i=t.length-1,a=this.getStyle("marker-start"),s=this.getStyle("marker-mid"),o=this.getStyle("marker-end");if(a.isUrlDefinition()){var u=a.getDefinition(),[l,h]=t[0];u.render(e,l,h)}if(s.isUrlDefinition())for(var v=s.getDefinition(),f=1;f<i;f++){var[c,g]=t[f];v.render(e,c,g)}if(o.isUrlDefinition()){var d=o.getDefinition(),[p,m]=t[i];d.render(e,p,m)}}}static pathM(e){var r=e.getAsCurrentPoint();return e.start=e.current,{point:r}}pathM(e,r){var{pathParser:t}=this,{point:i}=W.pathM(t),{x:a,y:s}=i;t.addMarker(i),r.addPoint(a,s),e&&e.moveTo(a,s)}static pathL(e){var{current:r}=e,t=e.getAsCurrentPoint();return{current:r,point:t}}pathL(e,r){var{pathParser:t}=this,{current:i,point:a}=W.pathL(t),{x:s,y:o}=a;t.addMarker(a,i),r.addPoint(s,o),e&&e.lineTo(s,o)}static pathH(e){var{current:r,command:t}=e,i=new Z((t.relative?r.x:0)+t.x,r.y);return e.current=i,{current:r,point:i}}pathH(e,r){var{pathParser:t}=this,{current:i,point:a}=W.pathH(t),{x:s,y:o}=a;t.addMarker(a,i),r.addPoint(s,o),e&&e.lineTo(s,o)}static pathV(e){var{current:r,command:t}=e,i=new Z(r.x,(t.relative?r.y:0)+t.y);return e.current=i,{current:r,point:i}}pathV(e,r){var{pathParser:t}=this,{current:i,point:a}=W.pathV(t),{x:s,y:o}=a;t.addMarker(a,i),r.addPoint(s,o),e&&e.lineTo(s,o)}static pathC(e){var{current:r}=e,t=e.getPoint("x1","y1"),i=e.getAsControlPoint("x2","y2"),a=e.getAsCurrentPoint();return{current:r,point:t,controlPoint:i,currentPoint:a}}pathC(e,r){var{pathParser:t}=this,{current:i,point:a,controlPoint:s,currentPoint:o}=W.pathC(t);t.addMarker(o,s,a),r.addBezierCurve(i.x,i.y,a.x,a.y,s.x,s.y,o.x,o.y),e&&e.bezierCurveTo(a.x,a.y,s.x,s.y,o.x,o.y)}static pathS(e){var{current:r}=e,t=e.getReflectedControlPoint(),i=e.getAsControlPoint("x2","y2"),a=e.getAsCurrentPoint();return{current:r,point:t,controlPoint:i,currentPoint:a}}pathS(e,r){var{pathParser:t}=this,{current:i,point:a,controlPoint:s,currentPoint:o}=W.pathS(t);t.addMarker(o,s,a),r.addBezierCurve(i.x,i.y,a.x,a.y,s.x,s.y,o.x,o.y),e&&e.bezierCurveTo(a.x,a.y,s.x,s.y,o.x,o.y)}static pathQ(e){var{current:r}=e,t=e.getAsControlPoint("x1","y1"),i=e.getAsCurrentPoint();return{current:r,controlPoint:t,currentPoint:i}}pathQ(e,r){var{pathParser:t}=this,{current:i,controlPoint:a,currentPoint:s}=W.pathQ(t);t.addMarker(s,a,a),r.addQuadraticCurve(i.x,i.y,a.x,a.y,s.x,s.y),e&&e.quadraticCurveTo(a.x,a.y,s.x,s.y)}static pathT(e){var{current:r}=e,t=e.getReflectedControlPoint();e.control=t;var i=e.getAsCurrentPoint();return{current:r,controlPoint:t,currentPoint:i}}pathT(e,r){var{pathParser:t}=this,{current:i,controlPoint:a,currentPoint:s}=W.pathT(t);t.addMarker(s,a,a),r.addQuadraticCurve(i.x,i.y,a.x,a.y,s.x,s.y),e&&e.quadraticCurveTo(a.x,a.y,s.x,s.y)}static pathA(e){var{current:r,command:t}=e,{rX:i,rY:a,xRot:s,lArcFlag:o,sweepFlag:u}=t,l=s*(Math.PI/180),h=e.getAsCurrentPoint(),v=new Z(Math.cos(l)*(r.x-h.x)/2+Math.sin(l)*(r.y-h.y)/2,-Math.sin(l)*(r.x-h.x)/2+Math.cos(l)*(r.y-h.y)/2),f=Math.pow(v.x,2)/Math.pow(i,2)+Math.pow(v.y,2)/Math.pow(a,2);f>1&&(i*=Math.sqrt(f),a*=Math.sqrt(f));var c=(o===u?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(a,2)-Math.pow(i,2)*Math.pow(v.y,2)-Math.pow(a,2)*Math.pow(v.x,2))/(Math.pow(i,2)*Math.pow(v.y,2)+Math.pow(a,2)*Math.pow(v.x,2)));isNaN(c)&&(c=0);var g=new Z(c*i*v.y/a,c*-a*v.x/i),d=new Z((r.x+h.x)/2+Math.cos(l)*g.x-Math.sin(l)*g.y,(r.y+h.y)/2+Math.sin(l)*g.x+Math.cos(l)*g.y),p=Bu([1,0],[(v.x-g.x)/i,(v.y-g.y)/a]),m=[(v.x-g.x)/i,(v.y-g.y)/a],y=[(-v.x-g.x)/i,(-v.y-g.y)/a],b=Bu(m,y);return Va(m,y)<=-1&&(b=Math.PI),Va(m,y)>=1&&(b=0),{currentPoint:h,rX:i,rY:a,sweepFlag:u,xAxisRotation:l,centp:d,a1:p,ad:b}}pathA(e,r){var{pathParser:t}=this,{currentPoint:i,rX:a,rY:s,sweepFlag:o,xAxisRotation:u,centp:l,a1:h,ad:v}=W.pathA(t),f=1-o?1:-1,c=h+f*(v/2),g=new Z(l.x+a*Math.cos(c),l.y+s*Math.sin(c));if(t.addMarkerAngle(g,c-f*Math.PI/2),t.addMarkerAngle(i,c-f*Math.PI),r.addPoint(i.x,i.y),e&&!isNaN(h)&&!isNaN(v)){var d=a>s?a:s,p=a>s?1:a/s,m=a>s?s/a:1;e.translate(l.x,l.y),e.rotate(u),e.scale(p,m),e.arc(0,0,d,h,h+v,!!(1-o)),e.scale(1/p,1/m),e.rotate(-u),e.translate(-l.x,-l.y)}}static pathZ(e){e.current=e.start}pathZ(e,r){W.pathZ(this.pathParser),e&&r.x1!==r.x2&&r.y1!==r.y2&&e.closePath()}}class kl extends W{constructor(e,r,t){super(e,r,t),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class He extends tr{constructor(e,r,t){super(e,r,new.target===He?!0:t),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;super.setContext(e,r);var t=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();t&&(e.textBaseline=t)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(e){if(this.type!=="text")return this.getTElementBoundingBox(e);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e);var r=null;return this.children.forEach((t,i)=>{var a=this.getChildBoundingBox(e,this,this,i);r?r.addBoundingBox(a):r=a}),r}getFontSize(){var{document:e,parent:r}=this,t=oe.parse(e.ctx.font).fontSize,i=r.getStyle("font-size").getNumber(t);return i}getTElementBoundingBox(e){var r=this.getFontSize();return new Pe(this.x,this.y-r,this.x+this.measureText(e),this.y)}getGlyph(e,r,t){var i=r[t],a=null;if(e.isArabic){var s=r.length,o=r[t-1],u=r[t+1],l="isolated";if((t===0||o===" ")&&t<s-1&&u!==" "&&(l="terminal"),t>0&&o!==" "&&t<s-1&&u!==" "&&(l="medial"),t>0&&o!==" "&&(t===s-1||u===" ")&&(l="initial"),typeof e.glyphs[i]<"u"){var h=e.glyphs[i];a=h instanceof kl?h:h[l]}}else a=e.glyphs[i];return a||(a=e.missingGlyph),a}getText(){return""}getTextFromNode(e){var r=e||this.node,t=Array.from(r.parentNode.childNodes),i=t.indexOf(r),a=t.length-1,s=sr(r.textContent||"");return i===0&&(s=Ec(s)),i===a&&(s=Rc(s)),s}renderChildren(e){if(this.type!=="text"){this.renderTElementChildren(e);return}this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e),this.children.forEach((t,i)=>{this.renderChild(e,this,this,i)});var{mouse:r}=this.document.screen;r.isWorking()&&r.checkBoundingBox(this,this.getBoundingBox(e))}renderTElementChildren(e){var{document:r,parent:t}=this,i=this.getText(),a=t.getStyle("font-family").getDefinition();if(a){for(var{unitsPerEm:s}=a.fontFace,o=oe.parse(r.ctx.font),u=t.getStyle("font-size").getNumber(o.fontSize),l=t.getStyle("font-style").getString(o.fontStyle),h=u/s,v=a.isRTL?i.split("").reverse().join(""):i,f=ge(t.getAttribute("dx").getString()),c=v.length,g=0;g<c;g++){var d=this.getGlyph(a,v,g);e.translate(this.x,this.y),e.scale(h,-h);var p=e.lineWidth;e.lineWidth=e.lineWidth*s/u,l==="italic"&&e.transform(1,0,.4,1,0,0),d.render(e),l==="italic"&&e.transform(1,0,-.4,1,0,0),e.lineWidth=p,e.scale(1/h,-1/h),e.translate(-this.x,-this.y),this.x+=u*(d.horizAdvX||a.horizAdvX)/s,typeof f[g]<"u"&&!isNaN(f[g])&&(this.x+=f[g])}return}var{x:m,y}=this;e.fillStyle&&e.fillText(i,m,y),e.strokeStyle&&e.strokeText(i,m,y)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var e=this.leafTexts[this.textChunkStart],r=e.getStyle("text-anchor").getString("start"),t=!1,i=0;r==="start"&&!t||r==="end"&&t?i=e.x-this.minX:r==="end"&&!t||r==="start"&&t?i=e.x-this.maxX:i=e.x-(this.minX+this.maxX)/2;for(var a=this.textChunkStart;a<this.leafTexts.length;a++)this.leafTexts[a].x+=i;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(e){this.children.forEach((r,t)=>{this.adjustChildCoordinatesRecursiveCore(e,this,this,t)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(e,r,t,i){var a=t.children[i];a.children.length>0?a.children.forEach((s,o)=>{r.adjustChildCoordinatesRecursiveCore(e,r,a,o)}):this.adjustChildCoordinates(e,r,t,i)}adjustChildCoordinates(e,r,t,i){var a=t.children[i];if(typeof a.measureText!="function")return a;e.save(),a.setContext(e,!0);var s=a.getAttribute("x"),o=a.getAttribute("y"),u=a.getAttribute("dx"),l=a.getAttribute("dy"),h=a.getStyle("font-family").getDefinition(),v=!!h&&h.isRTL;i===0&&(s.hasValue()||s.setValue(a.getInheritedAttribute("x")),o.hasValue()||o.setValue(a.getInheritedAttribute("y")),u.hasValue()||u.setValue(a.getInheritedAttribute("dx")),l.hasValue()||l.setValue(a.getInheritedAttribute("dy")));var f=a.measureText(e);return v&&(r.x-=f),s.hasValue()?(r.applyAnchoring(),a.x=s.getPixels("x"),u.hasValue()&&(a.x+=u.getPixels("x"))):(u.hasValue()&&(r.x+=u.getPixels("x")),a.x=r.x),r.x=a.x,v||(r.x+=f),o.hasValue()?(a.y=o.getPixels("y"),l.hasValue()&&(a.y+=l.getPixels("y"))):(l.hasValue()&&(r.y+=l.getPixels("y")),a.y=r.y),r.y=a.y,r.leafTexts.push(a),r.minX=Math.min(r.minX,a.x,a.x+f),r.maxX=Math.max(r.maxX,a.x,a.x+f),a.clearContext(e),e.restore(),a}getChildBoundingBox(e,r,t,i){var a=t.children[i];if(typeof a.getBoundingBox!="function")return null;var s=a.getBoundingBox(e);return s?(a.children.forEach((o,u)=>{var l=r.getChildBoundingBox(e,r,a,u);s.addBoundingBox(l)}),s):null}renderChild(e,r,t,i){var a=t.children[i];a.render(e),a.children.forEach((s,o)=>{r.renderChild(e,r,a,o)})}measureText(e){var{measureCache:r}=this;if(~r)return r;var t=this.getText(),i=this.measureTargetText(e,t);return this.measureCache=i,i}measureTargetText(e,r){if(!r.length)return 0;var{parent:t}=this,i=t.getStyle("font-family").getDefinition();if(i){for(var a=this.getFontSize(),s=i.isRTL?r.split("").reverse().join(""):r,o=ge(t.getAttribute("dx").getString()),u=s.length,l=0,h=0;h<u;h++){var v=this.getGlyph(i,s,h);l+=(v.horizAdvX||i.horizAdvX)*a/i.fontFace.unitsPerEm,typeof o[h]<"u"&&!isNaN(o[h])&&(l+=o[h])}return l}if(!e.measureText)return r.length*10;e.save(),this.setContext(e,!0);var{width:f}=e.measureText(r);return this.clearContext(e),e.restore(),f}getInheritedAttribute(e){for(var r=this;r instanceof He&&r.isFirstChild();){var t=r.parent.getAttribute(e);if(t.hasValue(!0))return t.getValue("0");r=r.parent}return null}}class Gr extends He{constructor(e,r,t){super(e,r,new.target===Gr?!0:t),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Jc extends Gr{constructor(){super(...arguments),this.type="textNode"}}class br extends tr{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(e){var r,{document:t}=this,{screen:i,window:a}=t,s=e.canvas;if(i.setDefaults(e),s.style&&typeof e.font<"u"&&a&&typeof a.getComputedStyle<"u"){e.font=a.getComputedStyle(s).getPropertyValue("font");var o=new D(t,"fontSize",oe.parse(e.font).fontSize);o.hasValue()&&(t.rootEmSize=o.getPixels("y"),t.emSize=t.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:u,height:l}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),v=this.getAttribute("refY"),f=this.getAttribute("viewBox"),c=f.hasValue()?ge(f.getString()):null,g=!this.root&&this.getStyle("overflow").getValue("hidden")!=="visible",d=0,p=0,m=0,y=0;c&&(d=c[0],p=c[1]),this.root||(u=this.getStyle("width").getPixels("x"),l=this.getStyle("height").getPixels("y"),this.type==="marker"&&(m=d,y=p,d=0,p=0)),i.viewPort.setCurrent(u,l),this.node&&(!this.parent||((r=this.node.parentNode)===null||r===void 0?void 0:r.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(e),e.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),c&&(u=c[2],l=c[3]),t.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:u,height:i.viewPort.height,desiredHeight:l,minX:d,minY:p,refX:h.getValue(),refY:v.getValue(),clip:g,clipX:m,clipY:y}),c&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(u,l))}clearContext(e){super.clearContext(e),this.document.screen.viewPort.removeCurrent()}resize(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=this.getAttribute("width",!0),a=this.getAttribute("height",!0),s=this.getAttribute("viewBox"),o=this.getAttribute("style"),u=i.getNumber(0),l=a.getNumber(0);if(t)if(typeof t=="string")this.getAttribute("preserveAspectRatio",!0).setValue(t);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(e),a.setValue(r),s.hasValue()||s.setValue("0 0 ".concat(u||e," ").concat(l||r)),o.hasValue()){var v=this.getStyle("width"),f=this.getStyle("height");v.hasValue()&&v.setValue("".concat(e,"px")),f.hasValue()&&f.setValue("".concat(r,"px"))}}}class Ll extends W{constructor(){super(...arguments),this.type="rect"}path(e){var r=this.getAttribute("x").getPixels("x"),t=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),a=this.getStyle("height",!1,!0).getPixels("y"),s=this.getAttribute("rx"),o=this.getAttribute("ry"),u=s.getPixels("x"),l=o.getPixels("y");if(s.hasValue()&&!o.hasValue()&&(l=u),o.hasValue()&&!s.hasValue()&&(u=l),u=Math.min(u,i/2),l=Math.min(l,a/2),e){var h=4*((Math.sqrt(2)-1)/3);e.beginPath(),a>0&&i>0&&(e.moveTo(r+u,t),e.lineTo(r+i-u,t),e.bezierCurveTo(r+i-u+h*u,t,r+i,t+l-h*l,r+i,t+l),e.lineTo(r+i,t+a-l),e.bezierCurveTo(r+i,t+a-l+h*l,r+i-u+h*u,t+a,r+i-u,t+a),e.lineTo(r+u,t+a),e.bezierCurveTo(r+u-h*u,t+a,r,t+a-l+h*l,r,t+a-l),e.lineTo(r,t+l),e.bezierCurveTo(r,t+l-h*l,r+u-h*u,t,r+u,t),e.closePath())}return new Pe(r,t,r+i,t+a)}getMarkers(){return null}}class ev extends W{constructor(){super(...arguments),this.type="circle"}path(e){var r=this.getAttribute("cx").getPixels("x"),t=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return e&&i>0&&(e.beginPath(),e.arc(r,t,i,0,Math.PI*2,!1),e.closePath()),new Pe(r-i,t-i,r+i,t+i)}getMarkers(){return null}}class rv extends W{constructor(){super(...arguments),this.type="ellipse"}path(e){var r=4*((Math.sqrt(2)-1)/3),t=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),a=this.getAttribute("cx").getPixels("x"),s=this.getAttribute("cy").getPixels("y");return e&&t>0&&i>0&&(e.beginPath(),e.moveTo(a+t,s),e.bezierCurveTo(a+t,s+r*i,a+r*t,s+i,a,s+i),e.bezierCurveTo(a-r*t,s+i,a-t,s+r*i,a-t,s),e.bezierCurveTo(a-t,s-r*i,a-r*t,s-i,a,s-i),e.bezierCurveTo(a+r*t,s-i,a+t,s-r*i,a+t,s),e.closePath()),new Pe(a-t,s-i,a+t,s+i)}getMarkers(){return null}}class tv extends W{constructor(){super(...arguments),this.type="line"}getPoints(){return[new Z(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new Z(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(e){var[{x:r,y:t},{x:i,y:a}]=this.getPoints();return e&&(e.beginPath(),e.moveTo(r,t),e.lineTo(i,a)),new Pe(r,t,i,a)}getMarkers(){var[e,r]=this.getPoints(),t=e.angleTo(r);return[[e,t],[r,t]]}}class jl extends W{constructor(e,r,t){super(e,r,t),this.type="polyline",this.points=[],this.points=Z.parsePath(this.getAttribute("points").getString())}path(e){var{points:r}=this,[{x:t,y:i}]=r,a=new Pe(t,i);return e&&(e.beginPath(),e.moveTo(t,i)),r.forEach(s=>{var{x:o,y:u}=s;a.addPoint(o,u),e&&e.lineTo(o,u)}),a}getMarkers(){var{points:e}=this,r=e.length-1,t=[];return e.forEach((i,a)=>{a!==r&&t.push([i,i.angleTo(e[a+1])])}),t.length>0&&t.push([e[e.length-1],t[t.length-1][1]]),t}}class iv extends jl{constructor(){super(...arguments),this.type="polygon"}path(e){var r=super.path(e),[{x:t,y:i}]=this.points;return e&&(e.lineTo(t,i),e.closePath()),r}}class av extends K{constructor(){super(...arguments),this.type="pattern"}createPattern(e,r,t){var i=this.getStyle("width").getPixels("x",!0),a=this.getStyle("height").getPixels("y",!0),s=new br(this.document,null);s.attributes.viewBox=new D(this.document,"viewBox",this.getAttribute("viewBox").getValue()),s.attributes.width=new D(this.document,"width","".concat(i,"px")),s.attributes.height=new D(this.document,"height","".concat(a,"px")),s.attributes.transform=new D(this.document,"transform",this.getAttribute("patternTransform").getValue()),s.children=this.children;var o=this.document.createCanvas(i,a),u=o.getContext("2d"),l=this.getAttribute("x"),h=this.getAttribute("y");l.hasValue()&&h.hasValue()&&u.translate(l.getPixels("x",!0),h.getPixels("y",!0)),t.hasValue()?this.styles["fill-opacity"]=t:Reflect.deleteProperty(this.styles,"fill-opacity");for(var v=-1;v<=1;v++)for(var f=-1;f<=1;f++)u.save(),s.attributes.x=new D(this.document,"x",v*o.width),s.attributes.y=new D(this.document,"y",f*o.height),s.render(u),u.restore();var c=e.createPattern(o,"repeat");return c}}class nv extends K{constructor(){super(...arguments),this.type="marker"}render(e,r,t){if(r){var{x:i,y:a}=r,s=this.getAttribute("orient").getString("auto"),o=this.getAttribute("markerUnits").getString("strokeWidth");e.translate(i,a),s==="auto"&&e.rotate(t),o==="strokeWidth"&&e.scale(e.lineWidth,e.lineWidth),e.save();var u=new br(this.document,null);u.type=this.type,u.attributes.viewBox=new D(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new D(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new D(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new D(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new D(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new D(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new D(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new D(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(e),e.restore(),o==="strokeWidth"&&e.scale(1/e.lineWidth,1/e.lineWidth),s==="auto"&&e.rotate(-t),e.translate(-i,-a)}}}class sv extends K{constructor(){super(...arguments),this.type="defs"}render(){}}class nn extends tr{constructor(){super(...arguments),this.type="g"}getBoundingBox(e){var r=new Pe;return this.children.forEach(t=>{r.addBoundingBox(t.getBoundingBox(e))}),r}}class Bl extends K{constructor(e,r,t){super(e,r,t),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:a}=this;a.forEach(s=>{s.type==="stop"&&i.push(s)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(e,r,t){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:a}=i,s=this.getGradient(e,r);if(!s)return this.addParentOpacity(t,a[a.length-1].color);if(a.forEach(p=>{s.addColorStop(p.offset,this.addParentOpacity(t,p.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:o}=this,{MAX_VIRTUAL_PIXELS:u,viewPort:l}=o.screen,[h]=l.viewPorts,v=new Ll(o,null);v.attributes.x=new D(o,"x",-u/3),v.attributes.y=new D(o,"y",-u/3),v.attributes.width=new D(o,"width",u),v.attributes.height=new D(o,"height",u);var f=new nn(o,null);f.attributes.transform=new D(o,"transform",this.getAttribute("gradientTransform").getValue()),f.children=[v];var c=new br(o,null);c.attributes.x=new D(o,"x",0),c.attributes.y=new D(o,"y",0),c.attributes.width=new D(o,"width",h.width),c.attributes.height=new D(o,"height",h.height),c.children=[f];var g=o.createCanvas(h.width,h.height),d=g.getContext("2d");return d.fillStyle=s,c.render(d),d.createPattern(g,"no-repeat")}return s}inheritStopContainer(e){this.attributesToInherit.forEach(r=>{!this.getAttribute(r).hasValue()&&e.getAttribute(r).hasValue()&&this.getAttribute(r,!0).setValue(e.getAttribute(r).getValue())})}addParentOpacity(e,r){if(e.hasValue()){var t=new D(this.document,"color",r);return t.addOpacity(e).getColor()}return r}}class ov extends Bl{constructor(e,r,t){super(e,r,t),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(e,r){var t=this.getGradientUnits()==="objectBoundingBox",i=t?r.getBoundingBox(e):null;if(t&&!i)return null;!this.getAttribute("x1").hasValue()&&!this.getAttribute("y1").hasValue()&&!this.getAttribute("x2").hasValue()&&!this.getAttribute("y2").hasValue()&&(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var a=t?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),s=t?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),o=t?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=t?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return a===o&&s===u?null:e.createLinearGradient(a,s,o,u)}}class uv extends Bl{constructor(e,r,t){super(e,r,t),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(e,r){var t=this.getGradientUnits()==="objectBoundingBox",i=r.getBoundingBox(e);if(t&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var a=t?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),s=t?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),o=a,u=s;this.getAttribute("fx").hasValue()&&(o=t?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=t?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var l=t?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return e.createRadialGradient(o,u,h,a,s,l)}}class lv extends K{constructor(e,r,t){super(e,r,t),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),a=this.getStyle("stop-opacity"),s=this.getStyle("stop-color",!0);s.getString()===""&&s.setValue("#000"),a.hasValue()&&(s=s.addOpacity(a)),this.offset=i,this.color=s.getColor()}}class sn extends K{constructor(e,r,t){super(e,r,t),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,e.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new D(e,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var e=this.getAttribute("attributeType").getString(),r=this.getAttribute("attributeName").getString();return e==="CSS"?this.parent.getStyle(r,!0):this.parent.getAttribute(r,!0)}calcValue(){var{initialUnits:e}=this,{progress:r,from:t,to:i}=this.getProgress(),a=t.getNumber()+(i.getNumber()-t.getNumber())*r;return e==="%"&&(a*=100),"".concat(a).concat(e)}update(e){var{parent:r}=this,t=this.getProperty();if(this.initialValue||(this.initialValue=t.getString(),this.initialUnits=t.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if(this.getAttribute("repeatCount").getString()==="indefinite"||this.getAttribute("repeatDur").getString()==="indefinite")this.duration=0;else if(i==="freeze"&&!this.frozen)this.frozen=!0,r.animationFrozen=!0,r.animationFrozenValue=t.getString();else if(i==="remove"&&!this.removed)return this.removed=!0,t.setValue(r.animationFrozen?r.animationFrozenValue:this.initialValue),!0;return!1}this.duration+=e;var a=!1;if(this.begin<this.duration){var s=this.calcValue(),o=this.getAttribute("type");if(o.hasValue()){var u=o.getString();s="".concat(u,"(").concat(s,")")}t.setValue(s),a=!0}return a}getProgress(){var{document:e,values:r}=this,t={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(r.hasValue()){var i=t.progress*(r.getValue().length-1),a=Math.floor(i),s=Math.ceil(i);t.from=new D(e,"from",parseFloat(r.getValue()[a])),t.to=new D(e,"to",parseFloat(r.getValue()[s])),t.progress=(i-a)/(s-a)}else t.from=this.from,t.to=this.to;return t}}class hv extends sn{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:e,from:r,to:t}=this.getProgress(),i=new qa(r.getColor()),a=new qa(t.getColor());if(i.ok&&a.ok){var s=i.r+(a.r-i.r)*e,o=i.g+(a.g-i.g)*e,u=i.b+(a.b-i.b)*e;return"rgb(".concat(Math.floor(s),", ").concat(Math.floor(o),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}}class cv extends sn{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:e,from:r,to:t}=this.getProgress(),i=ge(r.getString()),a=ge(t.getString()),s=i.map((o,u)=>{var l=a[u];return o+(l-o)*e}).join(" ");return s}}class vv extends K{constructor(e,r,t){super(e,r,t),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=e,{children:a}=this;for(var s of a)switch(s.type){case"font-face":{this.fontFace=s;var o=s.getStyle("font-family");o.hasValue()&&(i[o.getString()]=this);break}case"missing-glyph":this.missingGlyph=s;break;case"glyph":{var u=s;u.arabicForm?(this.isRTL=!0,this.isArabic=!0,typeof this.glyphs[u.unicode]>"u"&&(this.glyphs[u.unicode]=Object.create(null)),this.glyphs[u.unicode][u.arabicForm]=u):this.glyphs[u.unicode]=u;break}}}render(){}}class fv extends K{constructor(e,r,t){super(e,r,t),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class gv extends W{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class dv extends He{constructor(){super(...arguments),this.type="tref"}getText(){var e=this.getHrefAttribute().getDefinition();if(e){var r=e.children[0];if(r)return r.getText()}return""}}class pv extends He{constructor(e,r,t){super(e,r,t),this.type="a";var{childNodes:i}=r,a=i[0],s=i.length>0&&Array.from(i).every(o=>o.nodeType===3);this.hasText=s,this.text=s?this.getTextFromNode(a):""}getText(){return this.text}renderChildren(e){if(this.hasText){super.renderChildren(e);var{document:r,x:t,y:i}=this,{mouse:a}=r.screen,s=new D(r,"fontSize",oe.parse(r.ctx.font).fontSize);a.isWorking()&&a.checkBoundingBox(this,new Pe(t,i-s.getPixels("y"),t+this.measureText(e),i))}else if(this.children.length>0){var o=new nn(this.document,null);o.children=this.children,o.parent=this,o.render(e)}}onClick(){var{window:e}=this.document;e&&e.open(this.getHrefAttribute().getString())}onMouseMove(){var e=this.document.ctx;e.canvas.style.cursor="pointer"}}function Wu(n,e){var r=Object.keys(n);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(n);e&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),r.push.apply(r,t)}return r}function Rr(n){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Wu(Object(r),!0).forEach(function(t){La(n,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):Wu(Object(r)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))})}return n}class yv extends He{constructor(e,r,t){super(e,r,t),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(e){var{dataArray:r}=this;e&&e.beginPath(),r.forEach(t=>{var{type:i,points:a}=t;switch(i){case F.LINE_TO:e&&e.lineTo(a[0],a[1]);break;case F.MOVE_TO:e&&e.moveTo(a[0],a[1]);break;case F.CURVE_TO:e&&e.bezierCurveTo(a[0],a[1],a[2],a[3],a[4],a[5]);break;case F.QUAD_TO:e&&e.quadraticCurveTo(a[0],a[1],a[2],a[3]);break;case F.ARC:{var[s,o,u,l,h,v,f,c]=a,g=u>l?u:l,d=u>l?1:u/l,p=u>l?l/u:1;e&&(e.translate(s,o),e.rotate(f),e.scale(d,p),e.arc(0,0,g,h,h+v,!!(1-c)),e.scale(1/d,1/p),e.rotate(-f),e.translate(-s,-o));break}case F.CLOSE_PATH:e&&e.closePath();break}})}renderChildren(e){this.setTextData(e),e.save();var r=this.parent.getStyle("text-decoration").getString(),t=this.getFontSize(),{glyphInfo:i}=this,a=e.fillStyle;r==="underline"&&e.beginPath(),i.forEach((s,o)=>{var{p0:u,p1:l,rotation:h,text:v}=s;e.save(),e.translate(u.x,u.y),e.rotate(h),e.fillStyle&&e.fillText(v,0,0),e.strokeStyle&&e.strokeText(v,0,0),e.restore(),r==="underline"&&(o===0&&e.moveTo(u.x,u.y+t/8),e.lineTo(l.x,l.y+t/5))}),r==="underline"&&(e.lineWidth=t/20,e.strokeStyle=a,e.stroke(),e.closePath()),e.restore()}getLetterSpacingAt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return this.letterSpacingCache[e]||0}findSegmentToFitChar(e,r,t,i,a,s,o,u,l){var h=s,v=this.measureText(e,u);u===" "&&r==="justify"&&t<i&&(v+=(i-t)/a),l>-1&&(h+=this.getLetterSpacingAt(l));var f=this.textHeight/20,c=this.getEquidistantPointOnPath(h,f,0),g=this.getEquidistantPointOnPath(h+v,f,0),d={p0:c,p1:g},p=c&&g?Math.atan2(g.y-c.y,g.x-c.x):0;if(o){var m=Math.cos(Math.PI/2+p)*o,y=Math.cos(-p)*o;d.p0=Rr(Rr({},c),{},{x:c.x+m,y:c.y+y}),d.p1=Rr(Rr({},g),{},{x:g.x+m,y:g.y+y})}return h+=v,{offset:h,segment:d,rotation:p}}measureText(e,r){var{measuresCache:t}=this,i=r||this.getText();if(t.has(i))return t.get(i);var a=this.measureTargetText(e,i);return t.set(i,a),a}setTextData(e){if(!this.glyphInfo){var r=this.getText(),t=r.split(""),i=r.split(" ").length-1,a=this.parent.getAttribute("dx").split().map(x=>x.getPixels("x")),s=this.parent.getAttribute("dy").getPixels("y"),o=this.parent.getStyle("text-anchor").getString("start"),u=this.getStyle("letter-spacing"),l=this.parent.getStyle("letter-spacing"),h=0;!u.hasValue()||u.getValue()==="inherit"?h=l.getPixels():u.hasValue()&&u.getValue()!=="initial"&&u.getValue()!=="unset"&&(h=u.getPixels());var v=[],f=r.length;this.letterSpacingCache=v;for(var c=0;c<f;c++)v.push(typeof a[c]<"u"?a[c]:h);var g=v.reduce((x,T,E)=>E===0?0:x+T||0,0),d=this.measureText(e),p=Math.max(d+g,0);this.textWidth=d,this.textHeight=this.getFontSize(),this.glyphInfo=[];var m=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*m,b=0;(o==="middle"||o==="center")&&(b=-p/2),(o==="end"||o==="right")&&(b=-p),b+=y,t.forEach((x,T)=>{var{offset:E,segment:O,rotation:_}=this.findSegmentToFitChar(e,o,p,m,i,b,s,x,T);b=E,!(!O.p0||!O.p1)&&this.glyphInfo.push({text:t[T],p0:O.p0,p1:O.p1,rotation:_})})}}parsePathData(e){if(this.pathLength=-1,!e)return[];var r=[],{pathParser:t}=e;for(t.reset();!t.isEnd();){var{current:i}=t,a=i?i.x:0,s=i?i.y:0,o=t.next(),u=o.type,l=[];switch(o.type){case F.MOVE_TO:this.pathM(t,l);break;case F.LINE_TO:u=this.pathL(t,l);break;case F.HORIZ_LINE_TO:u=this.pathH(t,l);break;case F.VERT_LINE_TO:u=this.pathV(t,l);break;case F.CURVE_TO:this.pathC(t,l);break;case F.SMOOTH_CURVE_TO:u=this.pathS(t,l);break;case F.QUAD_TO:this.pathQ(t,l);break;case F.SMOOTH_QUAD_TO:u=this.pathT(t,l);break;case F.ARC:l=this.pathA(t);break;case F.CLOSE_PATH:W.pathZ(t);break}o.type!==F.CLOSE_PATH?r.push({type:u,points:l,start:{x:a,y:s},pathLength:this.calcLength(a,s,u,l)}):r.push({type:F.CLOSE_PATH,points:[],pathLength:0})}return r}pathM(e,r){var{x:t,y:i}=W.pathM(e).point;r.push(t,i)}pathL(e,r){var{x:t,y:i}=W.pathL(e).point;return r.push(t,i),F.LINE_TO}pathH(e,r){var{x:t,y:i}=W.pathH(e).point;return r.push(t,i),F.LINE_TO}pathV(e,r){var{x:t,y:i}=W.pathV(e).point;return r.push(t,i),F.LINE_TO}pathC(e,r){var{point:t,controlPoint:i,currentPoint:a}=W.pathC(e);r.push(t.x,t.y,i.x,i.y,a.x,a.y)}pathS(e,r){var{point:t,controlPoint:i,currentPoint:a}=W.pathS(e);return r.push(t.x,t.y,i.x,i.y,a.x,a.y),F.CURVE_TO}pathQ(e,r){var{controlPoint:t,currentPoint:i}=W.pathQ(e);r.push(t.x,t.y,i.x,i.y)}pathT(e,r){var{controlPoint:t,currentPoint:i}=W.pathT(e);return r.push(t.x,t.y,i.x,i.y),F.QUAD_TO}pathA(e){var{rX:r,rY:t,sweepFlag:i,xAxisRotation:a,centp:s,a1:o,ad:u}=W.pathA(e);return i===0&&u>0&&(u-=2*Math.PI),i===1&&u<0&&(u+=2*Math.PI),[s.x,s.y,r,t,o,u,a,i]}calcLength(e,r,t,i){var a=0,s=null,o=null,u=0;switch(t){case F.LINE_TO:return this.getLineLength(e,r,i[0],i[1]);case F.CURVE_TO:for(a=0,s=this.getPointOnCubicBezier(0,e,r,i[0],i[1],i[2],i[3],i[4],i[5]),u=.01;u<=1;u+=.01)o=this.getPointOnCubicBezier(u,e,r,i[0],i[1],i[2],i[3],i[4],i[5]),a+=this.getLineLength(s.x,s.y,o.x,o.y),s=o;return a;case F.QUAD_TO:for(a=0,s=this.getPointOnQuadraticBezier(0,e,r,i[0],i[1],i[2],i[3]),u=.01;u<=1;u+=.01)o=this.getPointOnQuadraticBezier(u,e,r,i[0],i[1],i[2],i[3]),a+=this.getLineLength(s.x,s.y,o.x,o.y),s=o;return a;case F.ARC:{a=0;var l=i[4],h=i[5],v=i[4]+h,f=Math.PI/180;if(Math.abs(l-v)<f&&(f=Math.abs(l-v)),s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],l,0),h<0)for(u=l-f;u>v;u-=f)o=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),a+=this.getLineLength(s.x,s.y,o.x,o.y),s=o;else for(u=l+f;u<v;u+=f)o=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),a+=this.getLineLength(s.x,s.y,o.x,o.y),s=o;return o=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],v,0),a+=this.getLineLength(s.x,s.y,o.x,o.y),a}}return 0}getPointOnLine(e,r,t,i,a){var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:r,o=arguments.length>6&&arguments[6]!==void 0?arguments[6]:t,u=(a-t)/(i-r+ir),l=Math.sqrt(e*e/(1+u*u));i<r&&(l*=-1);var h=u*l,v=null;if(i===r)v={x:s,y:o+h};else if((o-t)/(s-r+ir)===u)v={x:s+l,y:o+h};else{var f=0,c=0,g=this.getLineLength(r,t,i,a);if(g<ir)return null;var d=(s-r)*(i-r)+(o-t)*(a-t);d/=g*g,f=r+d*(i-r),c=t+d*(a-t);var p=this.getLineLength(s,o,f,c),m=Math.sqrt(e*e-p*p);l=Math.sqrt(m*m/(1+u*u)),i<r&&(l*=-1),h=u*l,v={x:f+l,y:c+h}}return v}getPointOnPath(e){var r=this.getPathLength(),t=0,i=null;if(e<-5e-5||e-5e-5>r)return null;var{dataArray:a}=this;for(var s of a){if(s&&(s.pathLength<5e-5||t+s.pathLength+5e-5<e)){t+=s.pathLength;continue}var o=e-t,u=0;switch(s.type){case F.LINE_TO:i=this.getPointOnLine(o,s.start.x,s.start.y,s.points[0],s.points[1],s.start.x,s.start.y);break;case F.ARC:{var l=s.points[4],h=s.points[5],v=s.points[4]+h;if(u=l+o/s.pathLength*h,h<0&&u<v||h>=0&&u>v)break;i=this.getPointOnEllipticalArc(s.points[0],s.points[1],s.points[2],s.points[3],u,s.points[6]);break}case F.CURVE_TO:u=o/s.pathLength,u>1&&(u=1),i=this.getPointOnCubicBezier(u,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3],s.points[4],s.points[5]);break;case F.QUAD_TO:u=o/s.pathLength,u>1&&(u=1),i=this.getPointOnQuadraticBezier(u,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3]);break}if(i)return i;break}return null}getLineLength(e,r,t,i){return Math.sqrt((t-e)*(t-e)+(i-r)*(i-r))}getPathLength(){return this.pathLength===-1&&(this.pathLength=this.dataArray.reduce((e,r)=>r.pathLength>0?e+r.pathLength:e,0)),this.pathLength}getPointOnCubicBezier(e,r,t,i,a,s,o,u,l){var h=u*Fu(e)+s*Uu(e)+i*Gu(e)+r*zu(e),v=l*Fu(e)+o*Uu(e)+a*Gu(e)+t*zu(e);return{x:h,y:v}}getPointOnQuadraticBezier(e,r,t,i,a,s,o){var u=s*Hu(e)+i*$u(e)+r*Yu(e),l=o*Hu(e)+a*$u(e)+t*Yu(e);return{x:u,y:l}}getPointOnEllipticalArc(e,r,t,i,a,s){var o=Math.cos(s),u=Math.sin(s),l={x:t*Math.cos(a),y:i*Math.sin(a)};return{x:e+(l.x*o-l.y*u),y:r+(l.x*u+l.y*o)}}buildEquidistantCache(e,r){var t=this.getPathLength(),i=r||.25,a=e||t/100;if(!this.equidistantCache||this.equidistantCache.step!==a||this.equidistantCache.precision!==i){this.equidistantCache={step:a,precision:i,points:[]};for(var s=0,o=0;o<=t;o+=i){var u=this.getPointOnPath(o),l=this.getPointOnPath(o+i);!u||!l||(s+=this.getLineLength(u.x,u.y,l.x,l.y),s>=a&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:o}),s-=a))}}}getEquidistantPointOnPath(e,r,t){if(this.buildEquidistantCache(r,t),e<0||e-this.getPathLength()>5e-5)return null;var i=Math.round(e/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var mv=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class bv extends tr{constructor(e,r,t){super(e,r,t),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var a=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);e.images.push(this),a?this.loadSvg(i):this.loadImage(i),this.isSvg=a}}loadImage(e){var r=this;return je(function*(){try{var t=yield r.document.createImage(e);r.image=t}catch(i){console.error('Error while loading image "'.concat(e,'":'),i)}r.loaded=!0})()}loadSvg(e){var r=this;return je(function*(){var t=mv.exec(e);if(t){var i=t[5];t[4]==="base64"?r.image=atob(i):r.image=decodeURIComponent(i)}else try{var a=yield r.document.fetch(e),s=yield a.text();r.image=s}catch(o){console.error('Error while loading image "'.concat(e,'":'),o)}r.loaded=!0})()}renderChildren(e){var{document:r,image:t,loaded:i}=this,a=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!(!i||!t||!o||!u)){if(e.save(),e.translate(a,s),this.isSvg){var l=r.canvg.forkString(e,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:o,scaleHeight:u});l.document.documentElement.parent=this,l.render()}else{var h=this.image;r.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:o,desiredWidth:h.width,height:u,desiredHeight:h.height}),this.loaded&&(typeof h.complete>"u"||h.complete)&&e.drawImage(h,0,0)}e.restore()}}getBoundingBox(){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),t=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new Pe(e,r,e+t,r+i)}}class xv extends tr{constructor(){super(...arguments),this.type="symbol"}render(e){}}class Ov{constructor(e){this.document=e,this.loaded=!1,e.fonts.push(this)}load(e,r){var t=this;return je(function*(){try{var{document:i}=t,a=yield i.canvg.parser.load(r),s=a.getElementsByTagName("font");Array.from(s).forEach(o=>{var u=i.createElement(o);i.definitions[e]=u})}catch(o){console.error('Error while loading font "'.concat(r,'":'),o)}t.loaded=!0})()}}class Fl extends K{constructor(e,r,t){super(e,r,t),this.type="style";var i=sr(Array.from(r.childNodes).map(s=>s.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")),a=i.split("}");a.forEach(s=>{var o=s.trim();if(o){var u=o.split("{"),l=u[0].split(","),h=u[1].split(";");l.forEach(v=>{var f=v.trim();if(f){var c=e.styles[f]||{};if(h.forEach(p=>{var m=p.indexOf(":"),y=p.substr(0,m).trim(),b=p.substr(m+1,p.length-m).trim();y&&b&&(c[y]=new D(e,y,b))}),e.styles[f]=c,e.stylesSpecificity[f]=Vc(f),f==="@font-face"){var g=c["font-family"].getString().replace(/"|'/g,""),d=c.src.getString().split(",");d.forEach(p=>{if(p.indexOf('format("svg")')>0){var m=_l(p);m&&new Ov(e).load(g,m)}})}}})}})}}Fl.parseExternalUrl=_l;class Sv extends tr{constructor(){super(...arguments),this.type="use"}setContext(e){super.setContext(e);var r=this.getAttribute("x"),t=this.getAttribute("y");r.hasValue()&&e.translate(r.getPixels("x"),0),t.hasValue()&&e.translate(0,t.getPixels("y"))}path(e){var{element:r}=this;r&&r.path(e)}renderChildren(e){var{document:r,element:t}=this;if(t){var i=t;if(t.type==="symbol"&&(i=new br(r,null),i.attributes.viewBox=new D(r,"viewBox",t.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new D(r,"preserveAspectRatio",t.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new D(r,"overflow",t.getAttribute("overflow").getString()),i.children=t.children,t.styles.opacity=new D(r,"opacity",this.calculateOpacity())),i.type==="svg"){var a=this.getStyle("width",!1,!0),s=this.getStyle("height",!1,!0);a.hasValue()&&(i.attributes.width=new D(r,"width",a.getString())),s.hasValue()&&(i.attributes.height=new D(r,"height",s.getString()))}var o=i.parent;i.parent=this,i.render(e),i.parent=o}}getBoundingBox(e){var{element:r}=this;return r?r.getBoundingBox(e):null}elementTransform(){var{document:e,element:r}=this;return Qe.fromElement(e,r)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Cr(n,e,r,t,i,a){return n[r*t*4+e*4+a]}function wr(n,e,r,t,i,a,s){n[r*t*4+e*4+a]=s}function ie(n,e,r){var t=n[e];return t*r}function Le(n,e,r,t){return e+Math.cos(n)*r+Math.sin(n)*t}class Ul extends K{constructor(e,r,t){super(e,r,t),this.type="feColorMatrix";var i=ge(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":{var a=i[0];i=[.213+.787*a,.715-.715*a,.072-.072*a,0,0,.213-.213*a,.715+.285*a,.072-.072*a,0,0,.213-.213*a,.715-.715*a,.072+.928*a,0,0,0,0,0,1,0,0,0,0,0,1];break}case"hueRotate":{var s=i[0]*Math.PI/180;i=[Le(s,.213,.787,-.213),Le(s,.715,-.715,-.715),Le(s,.072,-.072,.928),0,0,Le(s,.213,-.213,.143),Le(s,.715,.285,.14),Le(s,.072,-.072,-.283),0,0,Le(s,.213,-.213,-.787),Le(s,.715,-.715,.715),Le(s,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break}case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1];break}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(e,r,t,i,a){for(var{includeOpacity:s,matrix:o}=this,u=e.getImageData(0,0,i,a),l=0;l<a;l++)for(var h=0;h<i;h++){var v=Cr(u.data,h,l,i,a,0),f=Cr(u.data,h,l,i,a,1),c=Cr(u.data,h,l,i,a,2),g=Cr(u.data,h,l,i,a,3),d=ie(o,0,v)+ie(o,1,f)+ie(o,2,c)+ie(o,3,g)+ie(o,4,1),p=ie(o,5,v)+ie(o,6,f)+ie(o,7,c)+ie(o,8,g)+ie(o,9,1),m=ie(o,10,v)+ie(o,11,f)+ie(o,12,c)+ie(o,13,g)+ie(o,14,1),y=ie(o,15,v)+ie(o,16,f)+ie(o,17,c)+ie(o,18,g)+ie(o,19,1);s&&(d=0,p=0,m=0,y*=g/255),wr(u.data,h,l,i,a,0,d),wr(u.data,h,l,i,a,1,p),wr(u.data,h,l,i,a,2,m),wr(u.data,h,l,i,a,3,y)}e.clearRect(0,0,i,a),e.putImageData(u,0,0)}}class zr extends K{constructor(){super(...arguments),this.type="mask"}apply(e,r){var{document:t}=this,i=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(!s&&!o){var u=new Pe;this.children.forEach(g=>{u.addBoundingBox(g.getBoundingBox(e))}),i=Math.floor(u.x1),a=Math.floor(u.y1),s=Math.floor(u.width),o=Math.floor(u.height)}var l=this.removeStyles(r,zr.ignoreStyles),h=t.createCanvas(i+s,a+o),v=h.getContext("2d");t.screen.setDefaults(v),this.renderChildren(v),new Ul(t,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(v,0,0,i+s,a+o);var f=t.createCanvas(i+s,a+o),c=f.getContext("2d");t.screen.setDefaults(c),r.render(c),c.globalCompositeOperation="destination-in",c.fillStyle=v.createPattern(h,"no-repeat"),c.fillRect(0,0,i+s,a+o),e.fillStyle=c.createPattern(f,"no-repeat"),e.fillRect(0,0,i+s,a+o),this.restoreStyles(r,l)}render(e){}}zr.ignoreStyles=["mask","transform","clip-path"];var Xu=()=>{};class Tv extends K{constructor(){super(...arguments),this.type="clipPath"}apply(e){var{document:r}=this,t=Reflect.getPrototypeOf(e),{beginPath:i,closePath:a}=e;t&&(t.beginPath=Xu,t.closePath=Xu),Reflect.apply(i,e,[]),this.children.forEach(s=>{if(!(typeof s.path>"u")){var o=typeof s.elementTransform<"u"?s.elementTransform():null;o||(o=Qe.fromElement(r,s)),o&&o.apply(e),s.path(e),t&&(t.closePath=a),o&&o.unapply(e)}}),Reflect.apply(a,e,[]),e.clip(),t&&(t.beginPath=i,t.closePath=a)}render(e){}}class Hr extends K{constructor(){super(...arguments),this.type="filter"}apply(e,r){var{document:t,children:i}=this,a=r.getBoundingBox(e);if(a){var s=0,o=0;i.forEach(m=>{var y=m.extraFilterDistance||0;s=Math.max(s,y),o=Math.max(o,y)});var u=Math.floor(a.width),l=Math.floor(a.height),h=u+2*s,v=l+2*o;if(!(h<1||v<1)){var f=Math.floor(a.x),c=Math.floor(a.y),g=this.removeStyles(r,Hr.ignoreStyles),d=t.createCanvas(h,v),p=d.getContext("2d");t.screen.setDefaults(p),p.translate(-f+s,-c+o),r.render(p),i.forEach(m=>{typeof m.apply=="function"&&m.apply(p,0,0,h,v)}),e.drawImage(d,0,0,h,v,f-s,c-o,h,v),this.restoreStyles(r,g)}}}render(e){}}Hr.ignoreStyles=["filter","transform","clip-path"];class Ev extends K{constructor(e,r,t){super(e,r,t),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(e,r,t,i,a){}}class Rv extends K{constructor(){super(...arguments),this.type="feMorphology"}apply(e,r,t,i,a){}}class Cv extends K{constructor(){super(...arguments),this.type="feComposite"}apply(e,r,t,i,a){}}class wv extends K{constructor(e,r,t){super(e,r,t),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(e,r,t,i,a){var{document:s,blurRadius:o}=this,u=s.window?s.window.document.body:null,l=e.canvas;l.id=s.getUniqueId(),u&&(l.style.display="none",u.appendChild(l)),xc(l,r,t,i,a,o),u&&u.removeChild(l)}}class Pv extends K{constructor(){super(...arguments),this.type="title"}}class Av extends K{constructor(){super(...arguments),this.type="desc"}}var Iv={svg:br,rect:Ll,circle:ev,ellipse:rv,line:tv,polyline:jl,polygon:iv,path:W,pattern:av,marker:nv,defs:sv,linearGradient:ov,radialGradient:uv,stop:lv,animate:sn,animateColor:hv,animateTransform:cv,font:vv,"font-face":fv,"missing-glyph":gv,glyph:kl,text:He,tspan:Gr,tref:dv,a:pv,textPath:yv,image:bv,g:nn,symbol:xv,style:Fl,use:Sv,mask:zr,clipPath:Tv,filter:Hr,feDropShadow:Ev,feMorphology:Rv,feComposite:Cv,feColorMatrix:Ul,feGaussianBlur:wv,title:Pv,desc:Av};function Ku(n,e){var r=Object.keys(n);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(n);e&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),r.push.apply(r,t)}return r}function Nv(n){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ku(Object(r),!0).forEach(function(t){La(n,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):Ku(Object(r)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))})}return n}function _v(n,e){var r=document.createElement("canvas");return r.width=n,r.height=e,r}function Mv(n){return ka.apply(this,arguments)}function ka(){return ka=je(function*(n){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((t,i)=>{r.onload=()=>{t(r)},r.onerror=(a,s,o,u,l)=>{i(l)},r.src=n})}),ka.apply(this,arguments)}class ze{constructor(e){var{rootEmSize:r=12,emSize:t=12,createCanvas:i=ze.createCanvas,createImage:a=ze.createImage,anonymousCrossOrigin:s}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.canvg=e,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=e.screen,this.rootEmSize=r,this.emSize=t,this.createCanvas=i,this.createImage=this.bindCreateImage(a,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(e,r){return typeof r=="boolean"?(t,i)=>e(t,typeof i=="boolean"?i:r):e}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:e}=this;return e[e.length-1]}set emSize(e){var{emSizeStack:r}=this;r.push(e)}popEmSize(){var{emSizeStack:e}=this;e.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(e=>e.loaded)}isFontsLoaded(){return this.fonts.every(e=>e.loaded)}createDocumentElement(e){var r=this.createElement(e.documentElement);return r.root=!0,r.addStylesFromStyleDefinition(),this.documentElement=r,r}createElement(e){var r=e.nodeName.replace(/^[^:]+:/,""),t=ze.elementTypes[r];return typeof t<"u"?new t(this,e):new Wc(this,e)}createTextNode(e){return new Jc(this,e)}setViewBox(e){this.screen.setViewBox(Nv({document:this},e))}}ze.createCanvas=_v;ze.createImage=Mv;ze.elementTypes=Iv;function Qu(n,e){var r=Object.keys(n);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(n);e&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),r.push.apply(r,t)}return r}function Xe(n){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Qu(Object(r),!0).forEach(function(t){La(n,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):Qu(Object(r)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))})}return n}class cr{constructor(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.parser=new Ma(t),this.screen=new Ur(e,t),this.options=t;var i=new ze(this,t),a=i.createDocumentElement(r);this.document=i,this.documentElement=a}static from(e,r){var t=arguments;return je(function*(){var i=t.length>2&&t[2]!==void 0?t[2]:{},a=new Ma(i),s=yield a.parse(r);return new cr(e,s,i)})()}static fromString(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=new Ma(t),a=i.parseFromString(r);return new cr(e,a,t)}fork(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return cr.from(e,r,Xe(Xe({},this.options),t))}forkString(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return cr.fromString(e,r,Xe(Xe({},this.options),t))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var e=arguments,r=this;return je(function*(){var t=e.length>0&&e[0]!==void 0?e[0]:{};r.start(Xe({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},t)),yield r.ready(),r.stop()})()}start(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{documentElement:r,screen:t,options:i}=this;t.start(r,Xe(Xe({enableRedraw:!0},i),e))}stop(){this.screen.stop()}resize(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.documentElement.resize(e,r,t)}}export{pv as AElement,hv as AnimateColorElement,sn as AnimateElement,cv as AnimateTransformElement,Pe as BoundingBox,Fu as CB1,Uu as CB2,Gu as CB3,zu as CB4,cr as Canvg,ev as CircleElement,Tv as ClipPathElement,sv as DefsElement,Av as DescElement,ze as Document,K as Element,rv as EllipseElement,Ul as FeColorMatrixElement,Cv as FeCompositeElement,Ev as FeDropShadowElement,wv as FeGaussianBlurElement,Rv as FeMorphologyElement,Hr as FilterElement,oe as Font,vv as FontElement,fv as FontFaceElement,nn as GElement,kl as GlyphElement,Bl as GradientElement,bv as ImageElement,tv as LineElement,ov as LinearGradientElement,nv as MarkerElement,zr as MaskElement,Dl as Matrix,gv as MissingGlyphElement,Lc as Mouse,ir as PSEUDO_ZERO,Ma as Parser,W as PathElement,F as PathParser,av as PatternElement,Z as Point,iv as PolygonElement,jl as PolylineElement,D as Property,Hu as QB1,$u as QB2,Yu as QB3,uv as RadialGradientElement,Ll as RectElement,tr as RenderedElement,Uc as Rotate,br as SVGElement,Ov as SVGFontLoader,Gc as Scale,Ur as Screen,Vl as Skew,zc as SkewX,Hc as SkewY,lv as StopElement,Fl as StyleElement,xv as SymbolElement,dv as TRefElement,Gr as TSpanElement,He as TextElement,yv as TextPathElement,Pv as TitleElement,Qe as Transform,Fc as Translate,Wc as UnknownElement,Sv as UseElement,kc as ViewPort,sr as compressSpaces,cr as default,Vc as getSelectorSpecificity,wc as normalizeAttributeName,Pc as normalizeColor,_l as parseExternalUrl,Dv as presets,ge as toNumbers,Ec as trimLeft,Rc as trimRight,ju as vectorMagnitude,Bu as vectorsAngle,Va as vectorsRatio};
