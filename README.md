# Project Frontend

This is the user interface for our application.

## Prerequisites

-   Node.js (which includes npm)

## Getting Started

1.  **Install Dependencies:**
    Open a terminal in the frontend project directory and run:
    ```bash
    npm install
    ```

2.  **Run the Development Server:**
    To start the frontend application locally, run:
    ```bash
    npm run dev
    ```
    This will open the application in your default web browser. (`http://localhost:3000`).