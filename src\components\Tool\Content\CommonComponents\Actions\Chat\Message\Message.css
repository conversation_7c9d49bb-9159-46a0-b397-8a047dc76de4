/* Message bubble styling */
.message {
    padding: 8px;
    min-width: 256px;
    max-width: 80%;
    margin: 4px;
    clear: both;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Plain text message content */
.message .text {
    width: 100%;
    text-align: left;
    white-space: pre-wrap;
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
}

/* Markdown-rendered message content */
.message .message-content {
    width: 100%;
}

/* Prevent markdown content from breaking message layout */
.message .markdown-renderer {
    max-width: 100%;
    overflow-x: auto;
}

/* Adjust spacing for markdown elements within messages */
.message .markdown-renderer .markdown-paragraph:first-child {
    margin-top: 0;
}

.message .markdown-renderer .markdown-paragraph:last-child {
    margin-bottom: 0;
}

.message .markdown-renderer .markdown-heading:first-child {
    margin-top: 0;
}

.message .markdown-renderer .markdown-code-block {
    margin: 8px 0;
}

.message .markdown-renderer .markdown-table-wrapper {
    margin: 8px 0;
}

.message .markdown-renderer .markdown-blockquote {
    margin: 8px 0;
    white-space: pre-wrap !important;
}

/* Mobile responsive adjustments */
@media screen and (max-width: 1024px) {
    .message {
        padding: 6px;
        min-width: 200px;
        max-width: 85%;
    }

    .message .text {
        font-size: 12px;
    }
}