/* Markdown content styling for chat messages */
.markdown-renderer {
    width: 100%;
    text-align: left;
    line-height: 1.6;
    color: #333333;
    font-family: 'Open Sans', sans-serif;
}

/* Heading styles */
.markdown-heading {
    margin: 16px 0 8px 0;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #333333 !important;
}

.markdown-h1 {
    font-size: 28px;
    border-bottom: 2px solid #333333;
    padding-bottom: 8px;
    color: #333333 !important;
}

.markdown-h2 {
    font-size: 24px;
    border-bottom: 1px solid #333333;
    padding-bottom: 4px;
    color: #333333 !important;
}

.markdown-h3 {
    font-size: 20px;
    color: #333333 !important;
}

.markdown-h4 {
    font-size: 18px;
    color: #333333 !important;
}

.markdown-h5 {
    font-size: 16px;
    color: #333333 !important;
}

.markdown-h6 {
    font-size: 14px;
    color: #525367 !important;
}

/* Paragraph styling */
.markdown-paragraph {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.6;
    color: #333333 !important;
    white-space: pre-wrap;
}

/* Text formatting */
.markdown-strong {
    font-weight: bold;
    color: #333333 !important;
}

.markdown-emphasis {
    font-style: italic;
    color: #525367 !important;
}

/* Inline code styling */
.markdown-inline-code {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #6B8AFF !important;
}

/* Code block wrapper with header */
.markdown-code-wrapper {
    margin: 12px 0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.markdown-code-header {
    background-color: #2d3748;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.markdown-code-language {
    color: #a0aec0;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    font-weight: 600;
}

.markdown-copy-button {
    background-color: #4a5568 !important;
    color: #e2e8f0 !important;
    border: none !important;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: 'Open Sans', sans-serif;
    font-weight: 500;
    z-index: 10;
    position: relative;
}

.markdown-copy-button:hover {
    background-color: #4D79FF !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.markdown-copy-button:active {
    transform: scale(0.95);
}

.markdown-code-block {
    margin: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

.markdown-code-block pre {
    margin: 0 !important;
    padding: 16px !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    border-top: none !important;
}

/* Remove any gaps between header and code */
.markdown-code-wrapper .markdown-code-header + * {
    margin-top: 0 !important;
}

.markdown-code-wrapper > div:first-child + div {
    margin-top: 0 !important;
}

/* Plain code blocks (without syntax highlighting) */
.markdown-code-block-plain {
    background-color: #f8f8f8;
    border: none;
    border-radius: 0;
    margin: 0;
    padding: 16px;
    overflow-x: auto;
}

.markdown-code-plain {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #333 !important;
    background: none;
    border: none;
    padding: 0;
}

/* Lists */
.markdown-list {
    margin: 8px 0;
    padding-left: 40px; /* Increased padding for multi-digit numbers */
}

.markdown-unordered-list {
    list-style-type: disc;
    padding-left: 20px; /* Less padding for bullet points */
}

.markdown-ordered-list {
    list-style-type: decimal;
    padding-left: 40px; /* More padding for numbers */
}

.markdown-list-item {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.5;
    list-style: inherit;
    padding-left: 8px; /* Additional padding for content */
}

.markdown-list .markdown-list {
    margin: 4px 0;
    padding-left: 30px; /* Nested list padding */
}

.markdown-list .markdown-list .markdown-list-item {
    margin: 2px 0;
}

/* Ensure list numbers are fully visible */
ol.markdown-ordered-list {
    list-style-position: outside;
    margin-left: 0;
}

ul.markdown-unordered-list {
    list-style-position: outside;
    margin-left: 0;
}

/* Blockquote styling */
.markdown-blockquote {
    margin: 12px 0;
    padding: 12px 16px;
    border-left: 4px solid #4D79FF;
    background-color: #f5f5f5 !important;
    font-style: italic;
    color: #555 !important;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    white-space: pre-wrap !important;
    line-height: 1.6;
}

.markdown-blockquote .markdown-paragraph {
    margin: 4px 0;
    white-space: pre-wrap !important;
}

.markdown-blockquote p {
    margin: 4px 0 !important;
    color: #555 !important;
    white-space: pre-wrap !important;
}

/* Ensure blockquote content preserves formatting */
.markdown-blockquote * {
    white-space: pre-wrap !important;
    color: #555 !important;
}

/* Horizontal Rules */
.markdown-hr {
    margin: 20px 0;
    border: none;
    height: 2px;
    background-color: #ccc !important;
    border-radius: 1px;
}

/* Ensure all horizontal rule variations work */
hr {
    margin: 20px 0;
    border: none;
    height: 2px;
    background-color: #ccc !important;
    border-radius: 1px;
}

/* Links */
.markdown-link {
    color: #4D79FF !important;
    text-decoration: underline;
    border-bottom: none;
    transition: all 0.2s ease;
    font-weight: 500;
}

.markdown-link:hover {
    color: #2B4591 !important;
    text-decoration: underline;
    opacity: 0.8;
}

.markdown-link:visited {
    color: #6B46C1 !important;
}

.markdown-invalid-link {
    color: #FF4D4D !important;
    text-decoration: line-through;
}

/* Auto-detected URLs and emails */
.markdown-renderer a {
    color: #4D79FF !important;
    text-decoration: underline;
    font-weight: 500;
}

.markdown-renderer a:hover {
    color: #2B4591 !important;
    opacity: 0.8;
}

.markdown-renderer 
a:visited {
    color: #6B46C1 !important;
}

/* Tables */
.markdown-table-wrapper {
    margin: 12px 0;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #2d3748;
    background-color: #2d3748; /* Dark background like code blocks */
}

.markdown-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background-color: #2d3748; /* Dark background */
    margin: 0;
    color: #ffffff; /* Light text */
}

.markdown-table th,
.markdown-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #4a5568;
    border-right: 1px solid #4a5568;
    color: #ffffff !important; /* Light text with !important */
}

.markdown-table th {
    background-color: #1a202c !important; /* Even darker header */
    font-weight: 600;
    color: #4D79FF !important; /* Blue accent for headers */
    border-bottom: 2px solid #4D79FF;
    border-right: 1px solid #4a5568;
}

.markdown-table tr:nth-child(even) {
    background-color: #374151; /* Slightly lighter dark rows */
}

.markdown-table tr:hover {
    background-color: #4a5568; /* Hover effect */
}

.markdown-table tr:last-child td {
    border-bottom: none;
}

.markdown-table td:last-child,
.markdown-table th:last-child {
    border-right: none;
}

/* Horizontal rules */
.markdown-renderer hr {
    margin: 20px 0;
    border: none;
    height: 1px;
    background-color: #e0e0e0;
}

/* Message context overrides */
.message .markdown-renderer * {
    box-sizing: border-box;
}

.message .markdown-renderer {
    color: #333333 !important;
}

.message .markdown-renderer .markdown-heading {
    color: #333333 !important;
}

.message .markdown-renderer .markdown-paragraph {
    color: #333333 !important;
    white-space: pre-wrap !important;
}

.message .markdown-renderer .markdown-link,
.message .markdown-renderer a {
    color: #4D79FF !important;
}

.message .markdown-renderer .markdown-blockquote {
    background-color: #f5f5f5 !important;
    color: #555 !important;
}

.message .markdown-renderer .markdown-table th {
    background-color: #1a365d !important; /* Different blue-gray background */
    color: #ffffff !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border-right: 2px solid #4a5568 !important;
    border-bottom: 3px solid #4D79FF !important;
}

.message .markdown-renderer .markdown-table td {
    color: #e2e8f0 !important; /* Light gray text */
    background-color: #2d3748 !important;
    border-right: 2px solid #4a5568 !important;
    border-bottom: 2px solid #4a5568 !important;
}

.message .markdown-renderer .markdown-table {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important; /* Light gray text */
    border: 2px solid #4a5568 !important;
}

/* Responsive design */
@media screen and (max-width: 1024px) {
    .markdown-h1 {
        font-size: 24px;
    }

    .markdown-h2 {
        font-size: 20px;
    }

    .markdown-h3 {
        font-size: 18px;
    }

    .markdown-h4 {
        font-size: 16px;
    }

    .markdown-h5 {
        font-size: 14px;
    }

    .markdown-h6 {
        font-size: 12px;
    }

    .markdown-paragraph,
    .markdown-list-item {
        font-size: 12px;
    }

    .markdown-inline-code {
        font-size: 11px;
    }

    .markdown-code-block pre {
        font-size: 11px !important;
        padding: 12px !important;
    }

    .markdown-table {
        font-size: 12px;
    }

    .markdown-table th,
    .markdown-table td {
        padding: 6px 8px;
    }

    .markdown-copy-button {
        padding: 4px 8px;
        font-size: 10px;
    }
}

/* Dark theme support (if needed in the future) */
@media (prefers-color-scheme: dark) {
    .markdown-renderer {
        color: #e0e0e0;
    }
    
    .markdown-heading {
        color: #f0f0f0;
    }
    
    .markdown-inline-code {
        background-color: #2d2d2d;
        border-color: #444;
        color: #ff6b6b;
    }
    
    .markdown-blockquote {
        background-color: #1a1a2e;
        color: #ccc;
    }
    
    .markdown-table {
        background-color: #1a1a1a;
    }
    
    .markdown-table th {
        background-color: #2d2d2d;
        color: #f0f0f0;
    }
    
    .markdown-table tr:hover {
        background-color: #2a2a3e;
    }
}
