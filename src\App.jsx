import React, { useState, useEffect } from 'react';
import './App.css';

//Components
import Tool from './components/Tool/Tool';
import Login from './components/Login/Login';
import api from './api';
import {toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {BrowserRouter as Router} from "react-router-dom";
import LoadingBackdrop from "./reusable/LoadingBackdrop/LoadingBackdrop";
import ExpiredPasswordDialog from "./reusable/ExpiredPasswordDialog/ExpiredPasswordDialog";
import FastLoginDialog from "./reusable/FastLoginDialog/FastLoginDialog";
import HelperFunctions from "./reusable/HelperFunctions";

toast.configure();

const App = () => {

    const [loggedIn, setLoggedIn] = useState(localStorage.getItem('token')!== null);
    const [usernameInput, setUsernameInput] = useState("");
    const [passwordInput, setPasswordInput] = useState("");
    const [newPasswordInput, setNewPasswordInput] = useState("");
    const [abbreviationInput, setAbbreviationInput] = useState(localStorage.getItem('abbreviation'));
    const [fastLoginRequest, setFastLoginRequest] = useState(null);
    const [fastLogins, setFastLogins] = useState(null);
    const [username, setUsername] = useState(localStorage.getItem('username'));
    const [loading, setLoading] = useState(false);
    const [fastLoginDialog,setFastLoginDialog] = useState(false);
    const [expiredPasswordDialog, setExpiredPasswordDialog] = useState(false);
    const [screenSize, setScreenSize] = useState({
        width: window.innerWidth,
        height: window.innerHeight
    });

    useEffect(() => {
        const handleResize = () => {
            setScreenSize({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };

        window.addEventListener('resize', handleResize);

        // Clean up listener on unmount
        return () => window.removeEventListener('resize', handleResize);
    }, []);


    const loadFastLogins = () => {
        let abbreviation = localStorage.getItem("abbreviation");
        if(abbreviation) {
            let deviceToken = localStorage.getItem("deviceToken");
            let today = new Date().getDate();
            if(today.toString().length === 1) {
                today = "0" + today.toString();
            }
            if(!deviceToken || deviceToken.substring(0,2) !== today.toString()) {
                deviceToken = today + HelperFunctions.generateRandomString(16);
                localStorage.setItem("deviceToken",deviceToken);
            }
            const data = {
                abbreviation:abbreviationInput,
                deviceToken:deviceToken
            };
            api.post("/auth/fast-logins",data)
                .then(response => {
                    setFastLogins(response.data);
                })
                .catch(error => {
                    if(error.response?.data === "Die Zugangsdaten sind abgelaufen") {
                        showExpiredPasswordDialog();
                    }else if(error.response) {
                        showMessage(2,"Unternehmenskürzel, Benutzername und Passwort stimmen nicht über ein");
                        console.log(error.response)
                    } else if (error.request) {
                        showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                        console.log(error.request);
                    } else {
                        showMessage(2,"Etwas ist schiefgelaufen");
                        console.log(error.message);
                    }
                });
        }
    }

    //General actions

    const showMessage = (kind,message) => {
        if(kind === 0) {
            toast.success(message,{position:toast.POSITION.BOTTOM_CENTER,autoClose:3000});
        }else if(kind === 1) {
            toast.warning(message,{position:toast.POSITION.BOTTOM_CENTER,autoClose:3000});
        }else if(kind === 2) {
            toast.error(message,{position:toast.POSITION.BOTTOM_CENTER,autoClose:3000});
        }
    };

    //Login functions

    const loginUsernameChangeHandler = (e) => {
        setUsernameInput(e.target.value);
    };

    const loginPasswordChangeHandler = (e) => {
        setPasswordInput(e.target.value);
    };

    const loginNewPasswordChangeHandler = (e) => {
        setNewPasswordInput(e.target.value);
    };

    const loginAbbreviationChangeHandler = (e) => {
        setAbbreviationInput(e.target.value);
    };

    const loginClickHandler = () => {
        if(usernameInput !== '' && passwordInput !== '') {
            login();
        }else{
            showMessage(2,'Bitte füllen Sie alle Felder aus');
        }
    }

    const login = () => {
        const data = {
            abbreviation:abbreviationInput,
            username:usernameInput,
            password:passwordInput,
            deviceToken:localStorage.getItem("deviceToken")
        };
        api.post("/auth/login",data)
            .then(response => {
                localStorage.setItem('abbreviation',abbreviationInput);
                localStorage.setItem('username',username);
                localStorage.setItem('token',response.data.token);
                setLoggedIn(true);
            })
            .catch(error => {
                if(error.response.data === "Die Zugangsdaten sind abgelaufen") {
                    showExpiredPasswordDialog();
                }else if(error.response) {
                    showMessage(2,"Unternehmenskürzel, Benutzername und Passwort stimmen nicht über ein");
                    console.log(error.response)
                } else if (error.request) {
                    showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                    console.log(error.request);
                } else {
                    showMessage(2,"Etwas ist schiefgelaufen");
                    console.log(error.message);
                }
            });
    }

    const fastLogin = (fastLoginRequest) => {
        let data = {
            username: fastLoginRequest.username,
            abbreviation: localStorage.getItem("abbreviation"),
            deviceToken: localStorage.getItem("deviceToken"),
            pin: fastLoginRequest.pin
        }
        api.post("/auth/fast-login",data)
            .then(response => {
                console.log(response.data)
                closeFastLoginDialog();
                localStorage.setItem('abbreviation',abbreviationInput);
                localStorage.setItem('username',username);
                localStorage.setItem('token',response.data.token);
                setLoggedIn(true);
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    showMessage(2,"Etwas ist schiefgelaufen");
                    logout();
                }
            });
    }

    const logout = () => {
        if(true) {
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            const url = "/users/logout";
            return api.put(url)
                .then(
                    window.location.reload(false)
                );
        }
    }

    const changeExpiredPasswordOnClickHandler = () => {
        const data = {
            abbreviation:abbreviationInput,
            username:usernameInput,
            oldPassword:passwordInput,
            newPassword:newPasswordInput
        };
        api.put("/users/expired-password",data)
            .then(response => {
                localStorage.setItem('abbreviation',abbreviationInput);
                localStorage.setItem('username',username);
                localStorage.setItem('token',response.data.token);
                setLoggedIn(true);
                closeExpiredPasswordDialog();
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    showMessage(2,"Etwas ist schiefgelaufen");
                    logout();
                }
                setLoading(false);
            });
    }

    const showExpiredPasswordDialog = () => {
        setExpiredPasswordDialog(true);
    }

    const closeExpiredPasswordDialog = () => {
        setExpiredPasswordDialog(false);
    }

    const showFastLoginDialog = (fastLoginRequest) => {
        setFastLoginRequest(fastLoginRequest);
        setFastLoginDialog(true);
    }

    const closeFastLoginDialog = () => {
        setFastLoginRequest(null);
        setFastLoginDialog(null);
    }

    return (
        <Router>
            <ExpiredPasswordDialog
                open={expiredPasswordDialog}
                close={closeExpiredPasswordDialog}
                showMessage={showMessage}
                loginNewPasswordChangeHandler={loginNewPasswordChangeHandler}
                changeExpiredPasswordOnClickHandler={changeExpiredPasswordOnClickHandler}
            />

            {fastLoginDialog ? <FastLoginDialog
                open={fastLoginDialog}
                close={closeFastLoginDialog}
                showMessage={showMessage}
                fastLogin={fastLogin}
                fastLoginRequest={fastLoginRequest}
            /> : null}

            <div id="app" className='app'>
                {loading ? <LoadingBackdrop/> : null}
                <div className='app-content'>
                    {localStorage.getItem("token") !== null ?
                        <Tool
                            showMessage={showMessage}
                            username={usernameInput}
                            logout={logout}
                            setLoading={setLoading}
                            screenSize={screenSize}
                        /> :
                        <Login
                            showMessage={showMessage}
                            loginUsernameChangeHandler={loginUsernameChangeHandler}
                            loginPasswordChangeHandler={loginPasswordChangeHandler}
                            loginAbbreviationChangeHandler={loginAbbreviationChangeHandler}
                            loginClick={loginClickHandler}
                            setLoading={setLoading}
                            fastLogins={fastLogins}
                            showFastLoginDialog={showFastLoginDialog}
                            loadFastLogins={loadFastLogins}
                            screenSize={screenSize}
                        />
                    }
                </div>
            </div>
        </Router>
    )

}

export default App;
