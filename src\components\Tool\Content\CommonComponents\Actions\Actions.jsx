import React, {Component} from "react";
import './Actions.css';

//Components
import {NavLink, Route} from "react-router-dom";
import Dashboard from "./Dashboard/Dashboard";
import Settings from "./Settings/Settings";
import Administration from "./Administration/Administration";
import Chat from "./Chat/Chat";

class Actions extends Component {
    render() {
        return (
            <div className='actions actions-panel main-panel white-background'>
                <Route path='/administration'>
                    <menu className='actions-menu'>
                        <li>
                            <NavLink className='nav-item' activeClassName='active' exact
                                     to='/administration'><p>Benutzer</p>
                            </NavLink>
                        </li>
                        <li>
                            <NavLink className='nav-item' activeClassName='active'
                                     to='/administration/company-administration'><p>Unternehmensverwaltung</p></NavLink>
                        </li>
                        <li>
                            <NavLink className='nav-item' activeClassName='active'
                                     to='/administration/data-settings'><p>Dateneinstellungen</p></NavLink>
                        </li>
                    </menu>
                </Route>
                <Route path='/settings'>
                    <menu className='actions-menu'>
                        <li>
                            <NavLink className='nav-item' activeClassName='active' exact
                                     to='/settings'><p>Allgemein</p></NavLink>
                        </li>
                        <li>
                            <NavLink className='nav-item' activeClassName='active'
                                     to='/settings/security'><p>Sicherheit</p></NavLink>
                        </li>
                    </menu>
                </Route>
                <Route exact path='/'>
                    <Dashboard
                        user={this.props.user}
                        logout={this.props.logout}
                        setLoading={this.props.setLoading}
                        downloadFile={this.props.downloadFile}
                        formattedTransactions={this.props.formattedTransactions}
                        formattedCustomerOrders={this.props.formattedCustomerOrders}
                        formattedSupplierOrders={this.props.formattedSupplierOrders}
                        screenSize={this.props.screenSize}
                    />
                </Route>

                <Route path='/chat'>
                    <Chat
                        user={this.props.user}
                        logout={this.props.logout}
                        showMessage={this.props.showMessage}
                        attributes={this.props.attributes}
                        customers={this.props.customers}
                        suppliers={this.props.suppliers}
                        users={this.props.users}
                        setLoading={this.props.setLoading}
                        downloadFile={this.props.downloadFile}

                        // Conversations
                        conversation={this.props.conversation}
                        conversations={this.props.conversations}
                        setConversation={this.props.setConversation}
                        addConversationToList={this.props.addConversationToList}
                        updateConversationInList={this.props.updateConversationInList}
                        removeConversationFromList={this.props.removeConversationFromList}
                    />
                </Route>

                <Route path='/administration'>
                    <Administration
                        //Common
                        logout={this.props.logout}
                        showMessage={this.props.showMessage}
                        countries={this.props.countries}
                        setLoading={this.props.setLoading}
                        downloadFile={this.props.downloadFile}
                        user={this.props.user}

                        //Company
                        company={this.props.company}
                        updateCompany={this.props.updateCompany}

                        //Users
                        users={this.props.users}
                        addNewUser={this.props.addNewUser}
                        updateUser={this.props.updateUser}
                        deleteUser={this.props.deleteUser}
                        changePassword={this.props.changePassword}
                        changeAssignedLocation={this.props.changeAssignedLocation}

                        //Locations
                        locations={this.props.locations}

                        //Accounts
                        accounts={this.props.accounts}

                        //Article Groups
                        articleGroups={this.props.articleGroups}
                    />
                </Route>

                <Route path='/settings'>
                    <Settings
                        setUser={this.props.setUser}
                        logout={this.props.logout}
                        showMessage={this.props.showMessage}
                        changeOwnPassword={this.props.changeOwnPassword}
                        updateUserInformation={this.props.updateUserInformation}
                        user={this.props.user}
                        setLoading={this.props.setLoading}
                    />
                </Route>
            </div>
        )
    }
}

export default Actions;