import React, { useState } from "react";
import { useHistory, NavLink } from "react-router-dom";
import './Menu.css';

//Components
import DashboardIcon from "@mui/icons-material/Dashboard";
import SupervisorAccountIcon from "@mui/icons-material/SupervisorAccount";
import SettingsIcon from "@mui/icons-material/Settings";
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import {Tooltip} from "react-tooltip";
import MenuIcon from '@mui/icons-material/Menu';
import ChatIcon from '@mui/icons-material/Chat';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';

const Menu = (props) => {

    const [extended, setExtended] = useState(false);

    const history = useHistory();

    return (
        <div className="menu">
            <div className="mobile-bar">
                <MenuIcon onClick={() => setExtended(!extended)} className="icon toggle-menu-icon"/>
            </div>
            <div style={extended ? {width: "320px"} : props.screenSize.width > 1024 ? {width: "96px"} : {width: "0px"}} className='side-menu'>
                <Tooltip id="menu-tooltip" place="right"/>
                <div className="surrounder">
                    <AutoAwesomeIcon className="module-icon"/>
                    <ul className='navigation-items'>
                        <li>
                            <NavLink className='nav-item' activeClassName='active' exact to='/'>
                                <DashboardIcon className='icon'
                                               data-tooltip-id="menu-tooltip" data-tooltip-place="right"
                                               data-tooltip-content={extended ? "" : "Übersicht"}/><h2
                                style={extended ? {opacity: "1"} : {opacity: "0"}}>Übersicht</h2>
                            </NavLink>
                        </li>
                        <li>
                            <NavLink className='nav-item' activeClassName='active' exact to='/chat'>
                                <ChatIcon className='icon'
                                               data-tooltip-id="menu-tooltip" data-tooltip-place="right"
                                               data-tooltip-content={extended ? "" : "Übersicht"}/><h2
                                style={extended ? {opacity: "1"} : {opacity: "0"}}>Chat</h2>
                            </NavLink>
                        </li>
                        {props.user && props.user.role && props.user.role === "ADMIN" ? <li>
                            <NavLink className='nav-item' activeClassName='active'
                                     to='/administration'>
                                <SupervisorAccountIcon className='icon'
                                                       data-tooltip-id="menu-tooltip"
                                                       data-tooltip-place="right"
                                                       data-tooltip-content={extended ? "" : "Administration"}/>
                                <h2 style={extended ? {opacity: "1"} : {opacity: "0"}}>Administration</h2>
                            </NavLink>
                        </li> : null}
                    </ul>
                    <NavLink className='nav-item settings-nav-item' activeClassName='active'
                             to='/settings'>
                        <SettingsIcon className='icon settings-icon'
                                      data-tooltip-id="menu-tooltip" data-tooltip-place="right"
                                      data-tooltip-content={extended ? "" : "Einstellungen"}/><h2
                        style={extended ? {opacity: "1"} : {opacity: "0"}}>Einstellungen</h2>
                    </NavLink>
                    <a className="company-website" href="https://www.panthera-software.com" target="_blank"
                       rel="noopener noreferrer" style={extended ? {opacity: "1"} : {opacity: "0"}}>2025 Panthera
                        Software Solutions GmbH</a>
                </div>
                {props.screenSize.width > 1024 ? extended ? <ArrowBackIosIcon onClick={() => setExtended(false)} className="icon extend-icon"/> :
                    <ArrowForwardIosIcon onClick={() => setExtended(true)} className="icon extend-icon"/> : null}
            </div>
        </div>
    )
}


export default Menu;