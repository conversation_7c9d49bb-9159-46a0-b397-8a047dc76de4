import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import './ThemeToggle.css';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';

const ThemeToggle = ({ className = '' }) => {
    const { theme, toggleTheme } = useTheme();

    return (
        <button 
            className={`theme-toggle ${className}`}
            onClick={toggleTheme}
            title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        >
            {theme === 'light' ? (
                <DarkModeIcon className="theme-toggle-icon" />
            ) : (
                <LightModeIcon className="theme-toggle-icon" />
            )}
        </button>
    );
};

export default ThemeToggle;
