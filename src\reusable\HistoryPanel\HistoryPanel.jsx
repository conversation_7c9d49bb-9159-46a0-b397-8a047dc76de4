import React, { useState, useEffect } from 'react';
import './HistoryPanel.css';

//Components
import HistoryIcon from '@mui/icons-material/History';
import api from "../../api";
import HistoryEntry from "./HistoryEntry/HistoryEntry";
import LoadingScreen from "../../components/Tool/Content/CommonComponents/LoadingScreen/LoadingScreen";
import ToolTextField from "../ToolField/ToolTextField";
import MenuItem from "@mui/material/MenuItem";
import ToolButton from "../ToolButton/ToolButton";

const HistoryPanel = (props) => {

    const [historyEntries,setHistoryEntries] = useState(null)
    const [unfolded,setUnfolded] = useState(false)
    const [searchRequest,setSearchRequest] = useState({})

    const searchOnClick = () => {
        props.setLoading(true)
        let url = '/history-entries/search';
        api.post(url,searchRequest)
            .then(response => {
                setHistoryEntries(response.data)
                props.setLoading(false)
            })
            .catch(error => {
                props.logout();
            });
    }

    const open = () => {
        setUnfolded(true)
    }

    const close = () => {
        setUnfolded(false)
        setSearchRequest({})
        setHistoryEntries(null)
    }

    const toggleUnfolded = () => {
        if(!unfolded) {
            open();
        }else{
            close();
        }
    }

    let style;

    if(unfolded) {
        style = {
            width:"512px"
        }
    }else{
        style = {
            width:"0"
        }
    }

    const _handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            searchOnClick();
        }
    }

    const searchWordOnChangeHandler = (e) => {
        let updatedSearchRequest = { ...searchRequest }
        if(updatedSearchRequest) {
            updatedSearchRequest.searchWord = e.target.value
        }else{
            updatedSearchRequest = {
                searchWord: e.target.value
            }
        }
        setSearchRequest(updatedSearchRequest)
    }

    const searchTypeOnChangeHandler = (e) => {
        let updatedSearchRequest = { ...searchRequest }
        if(updatedSearchRequest) {
            updatedSearchRequest.searchType = e.target.value
        }else{
            updatedSearchRequest = {
                searchType: e.target.value
            }
        }
        setSearchRequest(updatedSearchRequest)
    }

    return (
        <div className='history-panel'>

            <div onClick={toggleUnfolded}
                 className="clickable toggle-button box-shadow-dark primary-color-background-light">
                <HistoryIcon className="history-icon"/>
            </div>
            <div className="content-li box-shadow-dark" style={style}>
                <div className="content-holder box-shadow-dark white-background">
                    <div className="search-bar">
                        <ToolTextField text='Suchwort' value={searchRequest?.searchWord ?? ''}
                                       onChange={searchWordOnChangeHandler}
                                       onKeyDown={_handleKeyDown}
                                       className='field search-input' label='Suchwort' size='small'
                                       InputLabelProps={{shrink: true}}/>
                        <ToolTextField value={searchRequest?.searchType ?? ""} onChange={searchTypeOnChangeHandler}
                                       size='small' select onKeyDown={_handleKeyDown}
                                       className='field search-select' label='Suche' InputLabelProps={{shrink: true}}>
                            <MenuItem key={0} value="">Keine</MenuItem>
                            <MenuItem key={1} value="CONVERSATION">Konversation</MenuItem>
                        </ToolTextField>

                        <ToolButton main className='search-button' onClick={searchOnClick}>
                            Suche
                        </ToolButton>
                        <p className="info-text">Es werden Aktionen der letzten 30 Tage angezeigt</p>
                    </div>
                    <div className="history-entries">
                        {historyEntries ? historyEntries.map((historyEntry, index) => (
                            <HistoryEntry
                                key={historyEntry.id}
                                index={index}
                                historyEntry={historyEntry}
                            />
                        )) : null}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default HistoryPanel;