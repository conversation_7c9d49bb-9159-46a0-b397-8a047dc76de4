import React, { useState, useEffect} from 'react';
import './UserDialog.css';

//Components
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import TextField from "@mui/material/TextField";
import ToolButton from "../../../../../../../../reusable/ToolButton/ToolButton";
import MenuItem from "@mui/material/MenuItem";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import {Tooltip} from "recharts";
import ToolTextField from "../../../../../../../../reusable/ToolField/ToolTextField";

const UserDialog = (props) => {

    const [user, setUser] = useState(props.user);

    useEffect(() => {
        setUser(props.user);
    }, []);

    const saveUserOnClickHandler = () => {
        if(validateUser()) {
            if(user && user.id) {
                props.updateUser(user);
                props.close();
            }else{
                if(user.username && user.password && user.passwordCheck) {
                    if(user.password === user.passwordCheck) {
                        props.addNewUser(user);
                        props.close();
                    }else{
                        props.showMessage(2,"Die Passwörter stimmen nicht über ein");
                    }
                }else{
                    props.showMessage(2,'Fehlende oder falsche Werte');
                }
            }
        }else{
            props.showMessage(2,"Fehlende oder falsche Werte")
        }
    }

    const validateUser = () => {
        if(user.username && user.role) {
            return true
        }else{
            return false
        }
    }

    //Change Handler
    const usernameOnChangeHandler = (e) => {
        setUser({ ...user, username: e.target.value });
    }

    const passwordOnChangeHandler = (e) => {
        setUser({ ...user, password: e.target.value });
    }

    const passwordCheckOnChangeHandler = (e) => {
        setUser({ ...user, passwordCheck: e.target.value });
    }

    const roleOnChangeHandler = (e) => {
        let updatedUser = { ...user }
        updatedUser.role = e.target.value
        setUser({ ...user, role: e.target.value });
    }

    return (
        <Dialog scroll='body' type={props.type} open={props.open} onClose={props.close} className='user-dialog'>

            <Tooltip id="user-dialog-tooltip"/>

            <div className='surrounder'>
                <DialogTitle className='title'>Benutzer {user?.id ? "bearbeiten" : "anlegen"}</DialogTitle>
                <ToolTextField size="small" value={user ? user.username : null} onChange={usernameOnChangeHandler}
                           className='field' label='Benutzername'/>
                {!user?.id ?
                    <ToolTextField size="small" value={user ? user.password : null} onChange={passwordOnChangeHandler}
                               className='field' label='Passwort' type='password'/> : null}
                {!user?.id ? <ToolTextField size="small" value={user ? user.passwordCheck : null}
                                              onChange={passwordCheckOnChangeHandler} className='field'
                                              label='Passwort wiederholen' type='password'/> : null}
                <ToolTextField size="small" value={user.role} onChange={roleOnChangeHandler} select className='field'
                           InputLabelProps={{shrink: true}} label='Rolle'>
                    {props.currentUser.role === "ADMIN" ? <MenuItem key={1} value="ADMIN">
                        Admin
                    </MenuItem> : null}
                    {props.currentUser.role === "ADMIN" ? <MenuItem key={2} value="USER">
                        Benutzer
                    </MenuItem> : null}
                </ToolTextField>
                <ToolButton main onClick={saveUserOnClickHandler} className='button'>Speichern</ToolButton>
            </div>
        </Dialog>
    )
}

export default UserDialog;