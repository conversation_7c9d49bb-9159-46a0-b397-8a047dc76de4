import React, {useEffect, useState} from "react";
import './UserMenu.css';
import {Route} from "react-router-dom";
import ToolTextField from "../../../../../reusable/ToolField/ToolTextField";
import MenuItem from "@mui/material/MenuItem";
import ThemeToggle from "../../../../ThemeToggle/ThemeToggle";

//Components

const UserMenu = (props) =>  {

    const [menuPanel,setMenuPanel] = useState(false)

    return (
        <div className='user-menu primary-color-background-very-light'>
            <Route exact path='/'>
                <h1 className="active-display">Übersicht</h1>
            </Route>
            <Route path='/chat'>
                <h1 className="active-display">Chat</h1>
            </Route>
            <Route path='/reporting'>
                <h1 className="active-display">Reporting</h1>
            </Route>
            <Route path='/administration'>
                <h1 className="active-display">Administration</h1>
            </Route>
            <Route path='/settings'>
                <h1 className="active-display">Einstellungen</h1>
            </Route>
            <div className="user-menu-controls">
                <ThemeToggle className="header-toggle" />
                <h2 className='UsernameText'
                    onClick={() => setMenuPanel(!menuPanel)}>{props.user ? props.user.username : 'Lädt...'}</h2>
            </div>
            {menuPanel ? <ul className='MenuPanel'>
                <li onClick={props.logout}>Logout</li>
            </ul> : null}
        </div>
    )
}

export default UserMenu;