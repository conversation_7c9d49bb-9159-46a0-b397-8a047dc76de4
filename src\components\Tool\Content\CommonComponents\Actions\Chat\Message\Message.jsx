import React from "react";
import './Message.css';
import ToolPaper from "../../../../../../../reusable/ToolPaper/ToolPaper";
import MarkdownRenderer from "./MarkdownRenderer";

const Message = (props) => {
    const messageText = props.message.text || '';
    
    return (
        <ToolPaper className="message" style={props.user.id === props.message.sender?.id ? {float:"right",background:"#D2DDFF"} : {float:"left"}}>
            <MarkdownRenderer content={messageText} className="message-content" />
        </ToolPaper>
    )
}

export default Message;
