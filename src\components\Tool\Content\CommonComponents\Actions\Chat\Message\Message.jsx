import React from "react";
import './Message.css';
import ToolPaper from "../../../../../../../reusable/ToolPaper/ToolPaper";
import MarkdownRenderer from "./MarkdownRenderer";

const Message = (props) => {
    const messageText = props.message.text || '';
    const isUserMessage = props.user.id === props.message.sender?.id;

    return (
        <ToolPaper
            className={`message ${isUserMessage ? 'user-message' : 'ai-message'}`}
            style={isUserMessage ? {float:"right"} : {float:"left"}}
        >
            <MarkdownRenderer content={messageText} className="message-content" />
        </ToolPaper>
    )
}

export default Message;
