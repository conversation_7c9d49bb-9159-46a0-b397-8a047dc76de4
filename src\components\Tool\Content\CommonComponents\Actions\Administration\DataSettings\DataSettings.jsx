import React, {useEffect, useState} from 'react';
import './DataSettings.css';

//Components
import HelperFunctions from "../../../../../../../reusable/HelperFunctions";
import api from "../../../../../../../api";
import ToolFab from "../../../../../../../reusable/ToolFab/ToolFab";
import DataSettingItem from "./DataSettingItem/DataSettingItem";
import SecureYesCancelDialog from "../../../../../../../reusable/SecureYesCancelDialog/SecureYesCancelDialog";
import TextDataSettingDialog from "./TextDataSettingDialog/TextDataSettingDialog";
import {Tooltip} from "react-tooltip";
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DescriptionIcon from '@mui/icons-material/Description';
import FileUploadDialog from "./FileUploadDialog/FileUploadDialog";

const DataSettings = (props) => {

    const [dataSetting,setDataSetting] = useState(null)
    const [dataSettings,setDataSettings] = useState(null)
    const [textDataSettingDialog,setTextDataSettingDialog] = useState(false)
    const [deleteDataSettingDialog,setDeleteDataSettingDialog] = useState(false)
    const [fileUploadDialog,setFileUploadDialog] = useState(false)

    useEffect(() => {
        loadDataSettings()
    }, []);

    useEffect(() => {
        console.log("interval wird initialisiert")
        const intervalId = setInterval(() => {
            loadDataSettings(true)
        }, 10000)
        return () => {
            clearInterval(intervalId);
        };
    }, []); // Empty dependency array => run once on mount

    const loadDataSettings = (noLoading) => {
        if(!textDataSettingDialog) {
            if(!noLoading) {
                props.setLoading(true)
            }
            api.get("/data-settings")
                .then(response => {
                    setDataSettings(response.data)
                    props.setLoading(false)
                })
                .catch(error => {
                    console.log(error);
                    if(error.response && error.response.data) {
                        props.showMessage(2,error.response.data);
                    } else if(!error.response && error.request) {
                        props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                    } else {
                        props.showMessage(2,"Etwas ist schiefgelaufen");
                    }
                    props.setLoading(false);
                });
        }
    }

    const addDataSettingToList = (dataSetting) => {
        const updatedDataSettings = [ ...dataSettings ]
        updatedDataSettings.unshift(dataSetting)
        setDataSettings(updatedDataSettings)
    }

    const updateDataSettingInList = (dataSetting) => {
        const updatedDataSettings = [ ...dataSettings ]
        for(let i = 0; i < updatedDataSettings.length; i++) {
            if(updatedDataSettings[i].id === dataSetting.id) {
                updatedDataSettings[i] = dataSetting
                break
            }
        }
        setDataSettings(updatedDataSettings)
    }

    const deleteDataSetting = () => {
        api.delete("/data-settings/" + dataSetting.id)
            .then(response => {
                removeDataSettingFromList(dataSetting)
                closeTextDataSettingDialog()
                props.showMessage(0,"Die Dateneinstellung wurde erfolgreich gelöscht");
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.setLoading(false);
                }
                props.setLoading(false);
            });
    }

    const removeDataSettingFromList = (dataSetting) => {
        setDataSettings(prev =>
            prev.filter(item => item.id !== dataSetting.id)
        )
    }

    const showTextDataSettingDialog = (dataSetting) => {
        setDataSetting(dataSetting)
        setTextDataSettingDialog(true)
    }

    const closeTextDataSettingDialog = () => {
        setTextDataSettingDialog(false)
        setDataSetting(null)
    }

    const showDeleteDataSettingDialog = (dataSetting) => {
        setDataSetting(dataSetting)
        setDeleteDataSettingDialog(true)
    }

    const closeDeleteDataSettingDialog = () => {
        setDeleteDataSettingDialog(false)
        setDataSetting(null)
    }

    return (
        <div className='data-settings'>

            {dataSetting && textDataSettingDialog ? <TextDataSettingDialog
                open={textDataSettingDialog}
                close={closeTextDataSettingDialog}
                showMessage={props.showMessage}
                logout={props.logout}
                setLoading={props.setLoading}
                dataSetting={dataSetting}
                addDataSettingToList={addDataSettingToList}
                updateDataSettingInList={updateDataSettingInList}
            /> : null}

            <FileUploadDialog
                open={fileUploadDialog}
                close={() => setFileUploadDialog(false)}
                showMessage={props.showMessage}
                setLoading={props.setLoading}
                dataSetting={dataSetting}
                addDataSettingToList={addDataSettingToList}
                updateDataSettingInList={updateDataSettingInList}
            />

            <SecureYesCancelDialog
                open={deleteDataSettingDialog}
                close={closeDeleteDataSettingDialog}
                header='Löschen'
                text='Wollen Sie die Dateneinstellung wirklich löschen?'
                onClick={deleteDataSetting}
                showMessage={props.showMessage}
            />


            <div className="data-setting-items">
                {dataSettings ? dataSettings.map(dataSetting => (
                    <DataSettingItem
                        key={dataSetting.id ? dataSetting.id : HelperFunctions.generateRandomString(5)}
                        dataSetting={dataSetting}
                        showTextDataSettingDialog={showTextDataSettingDialog}
                        showDeleteDataSettingDialog={showDeleteDataSettingDialog}
                        deleteDataSetting={showDeleteDataSettingDialog}
                        downloadFile={props.downloadFile}
                    />
                )) : null}
            </div>
            {dataSettings !== null && !dataSettings.some(ds => ds.status === "in_progress") ? <ToolFab className='file-add-fab' onClick={() => setFileUploadDialog(true)} aria-label="add">
                <UploadFileIcon/>
            </ToolFab> : null}
            {dataSettings !== null && !dataSettings.some(ds => ds.status === "in_progress") ? <ToolFab className='text-add-fab' onClick={() => showTextDataSettingDialog({})} aria-label="add">
                <DescriptionIcon/>
            </ToolFab> : null}
        </div>
    )
}

export default DataSettings;