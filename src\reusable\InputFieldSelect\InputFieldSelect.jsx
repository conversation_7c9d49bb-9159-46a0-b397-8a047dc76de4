import React, {Component} from "react";
import './InputFieldSelect.css';

class InputFieldSelect extends Component {
    render() {

        return (
            <div hidden={this.props.hidden} className={['InputFieldSelect', this.props.className].join(' ')}>
                <p className={this.props.disabled ? 'disabled' : null}><b>{this.props.text}</b></p>
                <select defaultValue={this.props.defaultValue} onChange={this.props.onChange} disabled={this.props.disabled}>
                    {this.props.options}
                </select>
            </div>
        )
    }
}

export default InputFieldSelect;