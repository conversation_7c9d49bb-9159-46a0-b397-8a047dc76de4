import React, {Component} from "react";
import './Settings.css';

//Components
import TextField from "@mui/material/TextField";
import ToolButton from "../../../../../../reusable/ToolButton/ToolButton";
import {Route} from "react-router-dom";
import api from "../../../../../../api";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import ToolTextField from "../../../../../../reusable/ToolField/ToolTextField";
import PinDialog from "./PinDialog/PinDialog";
import YesCancelDialog from "../../../../../../reusable/YesCancelDialog/YesCancelDialog";

class Settings extends Component {

    state = {
        changeOwnPasswordRequest:null,
        showPinDialog:false,
        showDeletePinDialog:false,
        user:this.props.user
    }

    oldPasswordOnChangeHandler = (e) => {
        let changeOwnPasswordRequest;
        if(this.state.changeOwnPasswordRequest) {
            changeOwnPasswordRequest = this.state.changeOwnPasswordRequest;
        }else{
            changeOwnPasswordRequest = {};
        }
        changeOwnPasswordRequest.oldPassword = e.target.value;
        this.setState({changeOwnPasswordRequest:changeOwnPasswordRequest});
    }

    newPasswordOnChangeHandler = (e) => {
        let changeOwnPasswordRequest;
        if(this.state.changeOwnPasswordRequest) {
            changeOwnPasswordRequest = this.state.changeOwnPasswordRequest;
        }else{
            changeOwnPasswordRequest = {};
        }
        changeOwnPasswordRequest.newPassword = e.target.value;
        this.setState({changeOwnPasswordRequest:changeOwnPasswordRequest});
    }

    checkPasswordOnChangeHandler = (e) => {
        let changeOwnPasswordRequest;
        if(this.state.changeOwnPasswordRequest) {
            changeOwnPasswordRequest = this.state.changeOwnPasswordRequest;
        }else{
            changeOwnPasswordRequest = {};
        }
        changeOwnPasswordRequest.checkPassword = e.target.value;
        this.setState({changeOwnPasswordRequest:changeOwnPasswordRequest});
    }

    changeOwnPasswordOnClickHandler = () => {
        if(this.state.changeOwnPasswordRequest && this.state.changeOwnPasswordRequest.newPassword !== '' && this.state.changeOwnPasswordRequest.checkPassword !== '') {
            if(this.state.changeOwnPasswordRequest.newPassword === this.state.changeOwnPasswordRequest.checkPassword) {
                this.props.changeOwnPassword(this.state.changeOwnPasswordRequest);
            }else{
                this.props.showMessage(2,'Das wiederholte Passwort entspricht nicht dem neuen Passwort');
            }
        }else{
            this.props.showMessage(2,'Bitte alle Felder ausfüllen');
        }
    }

    showPinDialog = () => {
        this.setState({showPinDialog:true});
    }

    closePinDialog = () => {
        this.setState({showPinDialog:false});
    }

    clearFields = () => {
        this.setState({changeOwnPasswordRequest:null});
    }

    deletePin = () => {
        api.put("/users/delete-pin", {
            headers: { "Content-Type": "text/plain" }, // Ensure the backend expects a raw string
        })
            .then(response => {
                this.props.setUser(response.data);
                this.props.showMessage(0,"Die PIN wurde erfolgreich gelöscht")
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    this.props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    this.props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    this.props.showMessage(2,"Etwas ist schiefgelaufen");
                    this.props.logout();
                }
                this.props.setLoading(false);
            });
    }

    showDeletePinDialog = () => {
        this.setState({showDeletePinDialog:true});
    }

    closeDeletePinDialog = () => {
        this.setState({showDeletePinDialog:false});
    }

    render() {

        let bookText;

        return (
            <div className='settings'>
                <Route exact path='/settings'>

                </Route>
                <Route exact path='/settings/security'>

                    <PinDialog
                        open={this.state.showPinDialog}
                        close={this.closePinDialog}
                        showMessage={this.props.showMessage}
                        logout={this.props.logout}
                        setLoading={this.props.setLoading}
                        user={this.props.user}
                        setUser={this.props.setUser}
                    />

                    <YesCancelDialog
                        open={this.state.showDeletePinDialog}
                        close={this.closeDeletePinDialog}
                        header='PIN Löschen'
                        text="Wollen Sie Ihre PIN wirklich löschen?"
                        onClick={this.deletePin}
                    />

                    <ToolTextField size="small" value={this.state.changeOwnPasswordRequest ? this.state.changeOwnPasswordRequest.oldPassword : null} onChange={this.oldPasswordOnChangeHandler} type='password' label='Altes Passwort' className='input'/>
                    <ToolTextField size="small" value={this.state.changeOwnPasswordRequest ? this.state.changeOwnPasswordRequest.newPassword : null} onChange={this.newPasswordOnChangeHandler} type='password' label='Neues Passwort' className='input'/>
                    <ToolTextField size="small" value={this.state.changeOwnPasswordRequest ? this.state.changeOwnPasswordRequest.checkPassword : null} onChange={this.checkPasswordOnChangeHandler} type='password' label='Passwort wiederholen' className='input'/>
                    <ToolButton onClick={this.showPinDialog} className='pin-button'>PIN hinterlegen</ToolButton>
                    {this.props.user && this.props.user.pin ? <ToolButton onClick={this.showDeletePinDialog} negative className='pin-button'>PIN entfernen</ToolButton> : null}
                    <ToolButton onClick={this.changeOwnPasswordOnClickHandler} main className='save-button'>Speichern</ToolButton>
                </Route>
            </div>
        )
    }
}

export default Settings;