/* CSS Variables for Theme Support */
:root {
    /* Light theme colors */
    --primary-color: #2B4591;
    --primary-color-light: #4D79FF;
    --primary-color-very-light: #D2DDFF;
    --secondary-color: #FF4D4D;
    --secondary-color-hover: #912B2B;

    /* Background colors */
    --background-primary: #FFFFFF;
    --background-secondary: #F8F9FA;
    --background-very-light: #D2DDFF;
    --background-hover: rgba(77, 121, 255, 0.1);

    /* Text colors */
    --text-primary: #525367;
    --text-secondary: #6C757D;
    --text-white: #FFFFFF;

    /* Border and shadow colors */
    --border-color: #E0E0E0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-light: rgba(255, 255, 255, 0.1);
}

/* Dark theme colors */
[data-theme="dark"] {
    --primary-color: #4D79FF;
    --primary-color-light: #6B8FFF;
    --primary-color-very-light: #2A3B5C;
    --secondary-color: #FF6B6B;
    --secondary-color-hover: #FF5252;

    /* Background colors */
    --background-primary: #1A1A1A;
    --background-secondary: #2D2D2D;
    --background-very-light: #2A3B5C;
    --background-hover: rgba(77, 121, 255, 0.2);

    /* Text colors */
    --text-primary: #E0E0E0;
    --text-secondary: #B0B0B0;
    --text-white: #FFFFFF;

    /* Border and shadow colors */
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-light: rgba(255, 255, 255, 0.05);
}

* {
    margin:0;
    padding:0;
    font-family: 'Open Sans', sans-serif;
    transition: all 300ms;
    -webkit-transition: all 300ms;
    -moz-transition: all 300ms;
    -o-transition: all 300ms;
    font-size:14px;
}

.primary-color {color: var(--primary-color);}
.primary-color-hover:hover {color: var(--primary-color);}
.primary-color-background {background-color: var(--primary-color);}
.primary-color-outline {outline: 1px solid var(--primary-color);}
.primary-color-background-hover:hover {background-color: var(--primary-color);}
.primary-color-outline-hover:hover {outline-color: var(--primary-color);}
.primary-color-light {color: var(--primary-color-light);}
.primary-color-background-light {background-color: var(--primary-color-light);}
.primary-color-border-light {border-color: var(--primary-color-light);}
.primary-color-outline-light {outline: 1px solid var(--primary-color-light);}
.primary-color-background-light-hover:hover {background-color: var(--primary-color-light);}

.primary-color-very-light {color: var(--primary-color-very-light);}
.primary-color-background-very-light {background-color: var(--background-very-light);}

.secondary-color {color: var(--secondary-color);}
.secondary-color-background {background-color: var(--secondary-color);}
.secondary-color-outline {outline-color: var(--secondary-color);}
.secondary-color-hover:hover {color: var(--secondary-color-hover);}
.secondary-color-outline-hover:hover {outline-color: var(--secondary-color-hover);}
.secondary-color-background-hover:hover {background-color: var(--secondary-color-hover);}
.box-shadow-dark {box-shadow: 0 0 20px 5px var(--shadow-color);}
.box-shadow-light {box-shadow: 0 0 20px 5px var(--shadow-light);}
.box-shadow-dark-hover:hover {box-shadow: 0 0 20px 5px var(--shadow-color);}
.box-shadow-light-hover:hover {box-shadow: 0 0 20px 5px var(--shadow-light);}

.white {color: var(--text-white);}
.white-background {background: var(--background-primary);}
.black {color: var(--text-primary);}
.black-background {background: var(--text-primary);}

p, li, input, select, option, label, table {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    color: var(--text-primary);
}

.clickable {
    cursor:pointer;
}

.clickable:hover {
    opacity:0.5;
}

li {
    list-style: none;
}

a {
    text-decoration: none;
    text-underline: none;
    color: var(--text-primary);
}

.modals {
    z-index:2000 !important;
}

.icon {
    font-size:14px;
    cursor:pointer;
    color: var(--primary-color-light);
}

.field {
    width:240px;
    text-align:left;
}

.tiny {
    width:64px;
    text-align:left;
}

.icon:hover {
    opacity:0.8;
}

.delete-icon {
    color: var(--secondary-color);
}

hr {
    height:1px;
    border:none;
    background-color: gray;
}

.fa-trash-alt {
    color: var(--secondary-color);
}

html, body {
    width:100%;
    height:100%;
    font-weight: 300;
    text-align: center;
}

.active-glow-object:hover span {
    box-shadow: 0 0 192px 192px rgba(77,121,255,0.8);
    filter: saturate(1);
}

.active-glow-object span {
    width:1px;
    height:1px;
    position: absolute;
    right: 0;
    bottom: 0;
    box-shadow: 0 0 128px 128px rgba(77,121,255,0.5);
    filter: saturate(0.8);
}

.deactivated-glow-object:hover span {
    box-shadow: 0 0 192px 192px rgba(255,77,77,0.8);
    filter: saturate(1);
}

.deactivated-glow-object span {
    width:1px;
    height:1px;
    position: absolute;
    right: 0;
    bottom: 0;
    box-shadow: 0 0 128px 128px rgba(255,77,77,0.5);
    filter: saturate(0.8);
}

.react-tooltip {
    color:#E8E8F0 !important;
    background:#4D79FF !important;
}

/*SCROLLBAR*/
::-webkit-scrollbar {width: 6px;}
::-webkit-scrollbar-track {background:transparent;}
::-webkit-scrollbar-thumb {background: var(--primary-color-light);border-radius:4px;}
::-webkit-scrollbar-thumb:hover {background: var(--primary-color);opacity:0.8;}
/*SELECTOR*/
::selection {color: var(--text-white);background: var(--text-primary);}

i:hover {
    opacity: 0.8;
    cursor:pointer;
}

h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 32px;
    font-weight: normal;
    color: var(--text-primary);
}

h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
    font-weight: normal;
    color: var(--text-primary);
}

h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: normal;
    color: var(--text-primary);
}

.MuiAutocomplete-popper {
    transition: all 0ms;
    -webkit-transition: all 0ms;
    -moz-transition: all 0ms;
    -o-transition: all 0ms;
}

.react-tooltip {
    z-index:10000;
}

.actions-panel {
    position:absolute;
    top:15%;
    left:1%;
    bottom:26%;
    right:26%;
}

.main-panel {
    border-radius:10px;
    box-shadow: 0 0 5px 1px var(--shadow-color);
}

.actions-panel .actions-menu {
    z-index:10;
    position:absolute;
    top:4px;
    left:5px;
    display:flex;
}

.actions-panel .actions-menu li {
    margin:8px;
}

.actions-panel .actions-menu .nav-item {
    display:block;
    border-radius:4px;
}

.actions-panel .actions-menu .nav-item:hover {
    background: var(--text-primary);
}

.actions-panel .actions-menu .nav-item p {
    color:#525367;
    padding:10px;
    font-weight:bold;
}

.actions-panel .actions-menu .nav-item:hover p {
    color:#E8E8F0;
}

.actions-panel .actions-menu .active {
    background:#525367;
}

.actions-panel .actions-menu .active p {
    color: #E8E8F0;
}

.circular-progress-div {
    width:100%;
    height:100%;
    justify-content:center;
    display:flex;
}

.failed {
    background:#FF4D4D !important;
}

.small-pulse {
    animation: small-pulse 2s ease-in infinite;
}

@keyframes small-pulse {
    0% {
        box-shadow: 0 0 8px rgba(77,121,255,0.4);
    }
    50% {
        box-shadow:0 0 16px rgba(77,121,255,0.8);
    }
    100% {
        box-shadow: 0 0 8px rgba(77,121,255,0.4);
    }
}

.medium-pulse {
    animation: medium-pulse 2s ease-in infinite;
}

@keyframes medium-pulse {
    0% {
        box-shadow: 0 0 12px rgba(77,121,255, 0.5);
    }
    50% {
        box-shadow:0 0 24px rgba(77,121,255, 1);
    }
    100% {
        box-shadow: 0 0 12px rgb(77,121,255, 0.5);
    }
}

.secondary-small-pulse {
    animation: secondary-small-pulse 2s ease-in infinite;
}

@keyframes secondary-small-pulse {
    0% {
        box-shadow: 0 0 0 rgba(255,77,77, 0.1);
    }
    50% {
        box-shadow:0 0 16px 8px rgba(255,77,77, 1);
    }
    100% {
        box-shadow: 0 0 0 rgb(255,77,77, 0.1);
    }
}

.secondary-big-pulse {
    animation: secondary-big-pulse 2s ease-in infinite;
}

@keyframes secondary-big-pulse {
    0% {
        box-shadow: 0 0 0 rgba(255,77,77, 0.1);
    }
    50% {
        box-shadow:0 0 64px 32px rgba(255,77,77, 1);
    }
    100% {
        box-shadow: 0 0 0 rgb(255,77,77, 0.1);
    }
}

@keyframes blink {
    0% {background:white;}
    50% {background:rgba(77,121,255,0.5);}
    100% {background:white;}
}

.blink {
    animation: blink 1s ease-in-out;
}

.blink-infinite {
    animation: blink 2s ease-in-out infinite;
}

@keyframes secondary-blink {
    0% {background: var(--background-primary);}
    50% {background: var(--secondary-color);}
    100% {background: var(--background-primary);}
}

.secondary-blink {
    animation: secondary-blink 1s ease-in-out;
}

.secondary-blink-infinite {
    animation: secondary-blink 2s ease-in-out infinite;
}

@media screen and (max-width: 1024px) {
    * {
        font-size:12px;
    }

    p, li, input, select, option, label, table {
        font-size: 12px;
    }

    i {
        font-size: 16px;
    }

    h1 {
        font-size: 20px;
    }

    h2 {
        font-size: 16px;
    }

    h3 {
        font-size: 14px;
    }

    .history-panel {
        display:none;
    }

    .actions-panel {
        bottom:1%;
        right:1%;
    }
}
