import React, {Component, useEffect} from 'react';
import './Content.css';

//Components
import BottomPanel from "./CommonComponents/BottomPanel/BottomPanel";
import Actions from "./CommonComponents/Actions/Actions";
import UserMenu from "./CommonComponents/UserMenu/UserMenu";
import Menu from "./CommonComponents/Menu/Menu";
import LoadingScreen from "./CommonComponents/LoadingScreen/LoadingScreen";
import HistoryPanel from "../../../reusable/HistoryPanel/HistoryPanel";
import SidePanel from "./CommonComponents/SidePanel/SidePanel";

const Content = (props) => {

    useEffect(() => {
        let loadRequest = {
            loadUser:props.user === null,
            loadUsers:props.users === null
        }
        let letRecentDataLoadRequest = {
            loadConversations:props.conversations === null
        }
        props.loadData(loadRequest,letRecentDataLoadRequest);
    }, []);

    let content;

    if(
        props.company !== null &&
        props.users !== null &&
        props.user !== null
    ) {
        content = <div className='surrounder'>
            {props.user && props.user.role === "ADMIN" ? <HistoryPanel
                setLoading={props.setLoading}
                showMessage={props.showMessage}
                logout={props.logout}
            /> : null}

            {props.company.logo && props.company.logoWidth && props.company.logoHeight ? <img className="company-logo" src={props.company.logo} alt="Unternehmenslogo" width={props.company.logoWidth} height={props.company.logoHeight}/> : null}

            <Menu
                user={props.user}
                company={props.company}
                screenSize={props.screenSize}
            />
            <div className='inside'>
                <UserMenu
                    //Data
                    user={props.user}
                    logout={props.logout}
                />
                <Actions
                    //Common
                    showMessage={props.showMessage}
                    setLoading={props.setLoading}
                    screenSize={props.screenSize}
                    downloadFile={props.downloadFile}

                    //User
                    user={props.user}
                    loadUser={props.loadUser}
                    changeUsername={props.changeUsername}
                    changePassword={props.changePassword}
                    changeOwnPassword={props.changeOwnPassword}
                    updateUserInformation={props.updateUserInformation}
                    updateDefaultCustomerOrderFilter={props.updateDefaultCustomerOrderFilter}
                    updateDefaultSupplierOrderFilter={props.updateDefaultSupplierOrderFilter}
                    logout={props.logout}

                    //Company
                    company={props.company}
                    updateCompany={props.updateCompany}

                    //Users
                    setUser={props.setUser}
                    users={props.users}
                    addNewUser={props.addNewUser}
                    updateUser={props.updateUser}
                    deleteUser={props.deleteUser}

                    // Conversations
                    conversation={props.conversation}
                    conversations={props.conversations}
                    setConversation={props.setConversation}
                    addConversationToList={props.addConversationToList}
                    updateConversationInList={props.updateConversationInList}
                    removeConversationFromList={props.removeConversationFromList}
                />
                <SidePanel
                    showMessage={props.showMessage}
                    setLoading={props.setLoading}

                    //User
                    user={props.user}

                    //Company
                    company={props.company}

                    // Conversations
                    conversation={props.conversation}
                    conversations={props.conversations}
                    setConversation={props.setConversation}
                    addConversationToList={props.addConversationToList}
                    deleteConversation={props.deleteConversation}
                    updateConversationInList={props.updateConversationInList}
                    removeConversationFromList={props.removeConversationFromList}
                />
                <BottomPanel
                    user={props.user}
                    setLoading={props.setLoading}
                />
            </div>
        </div>
    } else {
        content = <LoadingScreen/>
    }
    return (
        <div className='content primary-color-background-very-light'>
            {content}
        </div>
    )
}

export default Content;