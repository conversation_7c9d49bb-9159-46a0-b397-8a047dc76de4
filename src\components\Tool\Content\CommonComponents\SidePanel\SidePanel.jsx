import React, { useState } from "react";
import './SidePanel.css';
import ToolButton from "../../../../../reusable/ToolButton/ToolButton";
import CircularProgress from "@mui/material/CircularProgress";
import ConversationItem from "../../../../../reusable/ConversationItem/ConversationItem";
import YesCancelDialog from "../../../../../reusable/YesCancelDialog/YesCancelDialog";

//Components

const SidePanel = (props) => {

    const [deleteConversationDialog,setDeleteConversationDialog] = useState(false)

    return (
        <div className='side-panel main-panel white-background'>

            <YesCancelDialog
                open={deleteConversationDialog}
                close={() => setDeleteConversationDialog(false)}
                header='Konversation löschen'
                text='Wollen Sie die Konversation wirklich löschen? <PERSON><PERSON> könne<PERSON> dies nicht rückgängig machen.'
                onClick={() => props.deleteConversation(props.conversation)}
            />

            <ToolButton onClick={() => props.setConversation(null)} className="button conversation-button">Neue Konversation starten</ToolButton>
            <div className="conversation-list">
                {props.conversations !== null ? props.conversations.map(conversation => (
                    <ConversationItem
                        key={conversation.id}
                        conversation={conversation}
                        setConversation={props.setConversation}
                        deleteConversation={() => setDeleteConversationDialog(true)}
                    />
                )) : <CircularProgress className="loading-icon"/>}
            </div>
        </div>
    )
}

export default SidePanel;