import React, {Component, useState} from "react";
import "./ToolPaper.css";
import Paper from "@mui/material/Paper";

//Components

const ToolPaper = (props) => {

    return (
        <Paper { ...props } disabled={props.disabled} style={props.style} elevation={props.elevation ? props.elevation : 3} className={['tool-paper black', props.className].join(' ')} onClick={props.disabled ? null : props.onClick}>
            {props.children}
        </Paper>
    )
}

export default ToolPaper;