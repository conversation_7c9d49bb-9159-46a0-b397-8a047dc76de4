.theme-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 300ms ease;
    background-color: var(--background-secondary);
    color: var(--text-primary);
}

.theme-toggle:hover {
    background-color: var(--background-hover);
    opacity: 0.8;
}

.theme-toggle-icon {
    font-size: 20px !important;
    color: var(--primary-color);
}

.theme-toggle:hover .theme-toggle-icon {
    color: var(--primary-color-light);
}

/* Ensure proper styling in different contexts */
.theme-toggle.header-toggle {
    margin-left: 10px;
}

.theme-toggle.menu-toggle {
    width: 100%;
    justify-content: flex-start;
    padding: 10px;
    margin: 5px 0;
}
