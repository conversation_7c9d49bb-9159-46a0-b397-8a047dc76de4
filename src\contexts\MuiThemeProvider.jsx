import React from 'react';
import { createTheme, ThemeProvider as Mui<PERSON>hemeProvider } from '@mui/material/styles';
import { useTheme } from './ThemeContext';

const MuiThemeWrapper = ({ children }) => {
    const { theme } = useTheme();

    const muiTheme = createTheme({
        palette: {
            mode: theme,
            primary: {
                main: theme === 'light' ? '#4D79FF' : '#6B8FFF',
                light: theme === 'light' ? '#6B8FFF' : '#8FA7FF',
                dark: theme === 'light' ? '#2B4591' : '#4D79FF',
            },
            secondary: {
                main: theme === 'light' ? '#FF4D4D' : '#FF6B6B',
                light: theme === 'light' ? '#FF6B6B' : '#FF8A80',
                dark: theme === 'light' ? '#912B2B' : '#FF5252',
            },
            background: {
                default: theme === 'light' ? '#FFFFFF' : '#1A1A1A',
                paper: theme === 'light' ? '#FFFFFF' : '#2D2D2D',
            },
            text: {
                primary: theme === 'light' ? '#525367' : '#E0E0E0',
                secondary: theme === 'light' ? '#6C757D' : '#B0B0B0',
            },
        },
        components: {
            MuiPaper: {
                styleOverrides: {
                    root: {
                        backgroundColor: theme === 'light' ? '#FFFFFF' : '#2D2D2D',
                        color: theme === 'light' ? '#525367' : '#E0E0E0',
                    },
                },
            },
            MuiTextField: {
                styleOverrides: {
                    root: {
                        '& .MuiInputBase-root': {
                            backgroundColor: theme === 'light' ? '#FFFFFF' : '#2D2D2D',
                            color: theme === 'light' ? '#525367' : '#E0E0E0',
                        },
                        '& .MuiInputLabel-root': {
                            color: theme === 'light' ? '#525367' : '#B0B0B0',
                        },
                        '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme === 'light' ? '#E0E0E0' : '#404040',
                        },
                    },
                },
            },
        },
    });

    return (
        <MuiThemeProvider theme={muiTheme}>
            {children}
        </MuiThemeProvider>
    );
};

export default MuiThemeWrapper;
