import ReactDOM from 'react-dom';
import React, { useRef, useState } from 'react';
import Webcam from 'react-webcam';
import CloseIcon from '@mui/icons-material/Close';
import { Button } from '@mui/material';
import ToolButton from "../ToolButton/ToolButton";

const CameraFullscreen = (props) => {
    const webcamRef = useRef(null);
    const [photo, setPhoto] = useState(null);

    const videoConstraints = {
        facingMode: 'environment',
    };

    const capturePhoto = () => {
        const screenshot = webcamRef.current?.getScreenshot();
        if (screenshot) {
            setPhoto(screenshot);
        }
    };

    return ReactDOM.createPortal(
        <div
            className="camera"
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100vw',
                height: '100vh',
                zIndex: 1300,
                backgroundColor: 'black',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
            }}
        >
            <CloseIcon
                onClick={props.close}
                style={{
                    position: 'absolute',
                    top: '16px',
                    right: '16px',
                    width: '32px',
                    height: '32px',
                    color: 'white',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    borderRadius: '50%',
                    cursor: 'pointer',
                    zIndex: 1301,
                }}
            />

            {!photo ? (
                <>
                    <Webcam
                        audio={false}
                        ref={webcamRef}
                        screenshotFormat="image/jpeg"
                        videoConstraints={videoConstraints}
                        style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                        }}
                    />
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={capturePhoto}
                        style={{
                            position: 'absolute',
                            bottom: '32px',
                            zIndex: 1301,
                        }}
                    >
                        📸 Take Photo
                    </Button>
                </>
            ) : (
                <div style={{ textAlign: 'center', color: 'white' }}>
                    <img src={photo} alt="Captured" style={{ maxWidth: '100%', maxHeight: '80vh' }} />
                    <div style={{ marginTop: '16px' }}>
                        <ToolButton variant="contained" onClick={() => setPhoto(null)} style={{ marginRight: '10px' }}>
                            Retake
                        </ToolButton>
                        <ToolButton variant="outlined" color="secondary" onClick={props.close}>
                            Close
                        </ToolButton>
                    </div>
                </div>
            )}
        </div>,
        document.body
    );
};

export default CameraFullscreen;
