.article-stock-item {
    width:98%;
    height:72px;
    margin:8px auto;
    text-align:left;
    overflow:hidden;
    position:relative;
}

.article-stock-item .information-holder {
    width:94%;
    display:inline-block;
}

.article-stock-item .information-holder .option-li {
    width:13%;
    height:60px;
    float:left;
    margin:4px 0 0 8px;
    overflow-y:auto;
}

.article-stock-item .multiple-holder-li {
    width:100%;
    margin-bottom:4px;
}

.article-stock-item .edit-icon {
    padding:8px 2px 0 0;
    margin:0;
    float:right;
    cursor: pointer;
}

.article-stock-item .delete-icon {
    color:lightcoral;
    padding:8px 2px 0 0;
    margin:0;
    float:right;
    cursor: pointer;
}

.article-stock-item .order-status {
    height:100%;
    width:4px;
    float:left;
    border-top-left-radius:4px;
    border-bottom-left-radius:4px;
}

.article-stock-item .locked-info {
    margin-right:8px;
    color:grey;
    font-size:10px;
    float:right;
}

.article-stock-item .item-note {
    position:absolute;
    margin:8px 0 0 70%;
    color:grey;
}

.article-stock-item .document-positions-div p {
    font-size: 10px;
}

@media screen and (max-width: 1024px) {
    .article-stock-item .information-holder {
        width:98%;
        margin-left:2px;
    }
    .article-stock-item .information-holder .option-li {
        width:20%;
        margin-left:0;
    }
}