.menu .side-menu {
    top:0;
    left:0;
    bottom:0;
    width:96px;
    max-width:80%;
    position:absolute;
    justify-content: space-between;
    align-items:center;
    box-sizing: border-box;
    box-shadow: 0 0 5px 1px var(--shadow-color);
    z-index: 90;
    overflow:hidden;
    background: var(--background-primary);
}

.menu .mobile-bar {
    display:none;
    position:absolute;
    top:0;
    left:0;
    right:0;
    height:96px;
    background: var(--background-primary);
}

.menu .mobile-bar .toggle-menu-icon {
    float:right;
    margin:12px 12px 0 0;
    width:40px;
    height:40px;
}

.menu .side-menu .extend-icon {
    position:absolute;
    bottom:4px;
    right:4px;
    width:16px;
    height:16px;
}

.menu .side-menu .extend-icon:hover {
    color: var(--primary-color-light);
    filter: drop-shadow(0 0 8px rgba(77,121,255,0.8));
}

.menu .side-menu .surrounder {
    width:320px;
}

.menu .side-menu .icon {
    font-size:32px;
    color: var(--text-primary);
}

.menu .side-menu .settings-nav-item {
    font-size:32px;
    position: absolute;
    bottom:64px;
    left:32px;
    display:flex;
}

.menu .side-menu .icon-holder:hover .arrow-icon {
    opacity:0.8;
}

.menu .side-menu .arrow-icon {
    font-size: 48px;
    margin-top:16px;
}

.menu .side-menu .module-icon {
    position:absolute;
    top:52px;
    display:flex;
    width:32px;
    height:32px;
    margin-left: 32px;
    color: var(--primary-color-light);
}

.menu .side-menu .module-button {
    opacity:0;
    margin:160px auto 0 auto;
    width:160px;
}

.menu .side-menu .top-panel {
    position: fixed;
    z-index: 100;
    left:0;
    right:0;
    top:0;
    height:64px;
    display:none;
    box-shadow: 0 0 5px 1px var(--shadow-color);
}

.menu .side-menu .top-panel i {
    font-size: 28px;
    float:right;
    margin:18px 18px 0 0;
}

.menu .side-menu .navigation-items {
    text-align:left;
    position: absolute;
    left:0;
    top:30%;
    width:320px;
    bottom:128px;
    overflow-y:auto;
}

.menu .side-menu .navigation-items li {
    margin:24px 0 0 32px;
    display:flex;
}

.menu .side-menu h2 {
    color: var(--text-primary);
    opacity: 0;
    float:right;
    margin:2px 0 0 32px;
}

.menu .side-menu .active .icon {
    color: var(--primary-color-light);
    filter: drop-shadow(0 0 8px rgba(77,121,255,0.8));
}

.menu .side-menu .navigation-items li:hover .icon {
    color: var(--primary-color-light);
    filter: drop-shadow(0 0 8px rgba(77,121,255,0.8));
}

.menu .side-menu .company-website {
    text-align:left;
    position:absolute;
    bottom:32px;
    left:32px;
    font-size: 10px;
    width:256px;
    opacity:0;
}

.menu .side-menu .settings-nav-item:hover .icon {
    color: var(--primary-color-light);
    filter: drop-shadow(0 0 8px rgba(77,121,255,0.8));
}

.menu .side-menu .side-menu:hover .company-website {
    opacity:1;
}

@media screen and (max-width: 1024px) {
    .menu .mobile-bar {
        display:block;
    }
    .menu .side-menu .module-button {
        margin-top:112px;
    }

    .menu .side-menu .navigation-items {
        top:160px;
    }
    .menu .side-menu .navigation-items li {
        margin-top:16px;
    }
}