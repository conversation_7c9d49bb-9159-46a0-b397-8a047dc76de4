import React, { useState, useEffect } from 'react';
import './DataSettingItem.css';

// Components
import DeleteIcon from '@mui/icons-material/Delete';
import ArticleIcon from '@mui/icons-material/Article';
import ToolPaper from "../../../../../../../../reusable/ToolPaper/ToolPaper";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import DownloadIcon from '@mui/icons-material/Download';

const DataSettingItem = (props) => {

    return (
        <ToolPaper elevation={3} className={`data-setting-item ${
            props.dataSetting.status === "in_progress" ? "blink-infinite" : props.dataSetting.status === "failed" ? "failed" : ""
        }`}>

            {props.dataSetting.status !== "in_progress" ? <DeleteIcon
                onClick={() => props.deleteDataSetting(props.dataSetting)}
                className='icon delete-icon'
            /> : null}
            {props.dataSetting.status !== "in_progress" && props.dataSetting.text ? <ArticleIcon
                onClick={() => props.showTextDataSettingDialog(props.dataSetting)}
                className='icon edit-icon'
            /> : null}
            {props.dataSetting.status !== "in_progress" && props.dataSetting.fileStorageLocation ? <DownloadIcon
                onClick={() => props.downloadFile(props.dataSetting.fileStorageLocation)}
                className='icon edit-icon'
            /> : null}

            {props.dataSetting.status === "in_progress" ? <p className="status-info">wird verarbeitet...</p> : null}
            {props.dataSetting.status === "failed" ? <p className="status-info">Verarbeitung fehlgeschlagen</p> : null}

            <ul className="information-holder">
                <li className="option-li">{props?.dataSetting?.name}</li>
            </ul>
        </ToolPaper>
    );
};

export default DataSettingItem;