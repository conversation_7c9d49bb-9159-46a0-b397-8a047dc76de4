import React, {Component, useState} from "react";
import './FileDialog.css';

//Components
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import ToolButton from "../ToolButton/ToolButton";
import CloseIcon from '@mui/icons-material/Close';
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";

const FileDialog = (props) => {

    const [multipartFile,setMultipartFile] = useState(null)
    const [errormessage,setErrormessage] = useState(null)

    const fileOnChangeHandler = (e) => {
        let file = e.target.files[0];
        if(file.name.length > 127) {
            setErrormessage("Der Dateiname darf maximal 127 Zeichen lang sein")
        }else if(file.size > 10000000){
            setErrormessage("Die Datei darf maximal 10MB groß sein")
        }else{
            setErrormessage(null)
            setMultipartFile(file)
        }
    }

    const uploadFileOnClickHandler = () => {
        if (multipartFile.file !== null) {
            props.uploadFile(multipartFile);
            props.close();
        } else {
            props.showMessage(2, 'Fehlende oder falsche Werte');
        }
    }

    return (
        <Dialog scroll='body' open={props.open} onClose={props.close} className='file-dialog'>
            <DialogTitle>Datei hochladen</DialogTitle>
            <DialogContent>
                <CloseIcon onClick={props.close} className='icon close-icon'/>
                <input className='input file-selector' type="file"  accept=".txt,.pdf,.png,.jpg,.jpeg,.xls,.xlsx" onChange={fileOnChangeHandler} />
                <p className="errormessage">{errormessage}</p>
                <DialogActions>
                    <ToolButton main disabled={errormessage !== ''} className='button' onClick={uploadFileOnClickHandler}>Bestätigen</ToolButton>
                </DialogActions>
            </DialogContent>
        </Dialog>
    )
}

export default FileDialog;