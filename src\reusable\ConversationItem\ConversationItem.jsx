import React, { useState, useEffect } from 'react';
import './ConversationItem.css';

// Components
import DeleteIcon from '@mui/icons-material/Delete';
import ArticleIcon from '@mui/icons-material/Article';
import Paper from '@mui/material/Paper';
import HelperFunctions from "../HelperFunctions";
import ToolPaper from "../ToolPaper/ToolPaper";
import {Tooltip} from "react-tooltip";

const ConversationItem = (props) => {

    return (
        <ToolPaper onClick={() => props.setConversation(props.conversation)} elevation={3} className='conversation-item'>
            <DeleteIcon
                onClick={() => props.deleteConversation(props.conversation)}
                className='icon delete-icon'
            />
            <p className="name">{props.conversation.name}</p>
            <p className="date">{props.conversation.dateTime ? "Konversation gestartet " + HelperFunctions.formatDateTime(props.conversation.dateTime) : null}</p>
        </ToolPaper>
    );
};

export default ConversationItem;