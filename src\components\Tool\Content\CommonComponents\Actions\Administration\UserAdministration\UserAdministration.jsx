import React, { useState, useEffect } from 'react';
import './UserAdministration.css';

//Components
import ToolFab from "../../../../../../../reusable/ToolFab/ToolFab";
import AddIcon from '@mui/icons-material/Add';
import UserDialog from "./UserDialog/UserDialog";
import YesCancelDialog from "../../../../../../../reusable/YesCancelDialog/YesCancelDialog";
import UserItem from "./UserItem/UserItem";

const UserAdministration = (props) => {

    const [user, setUser] = useState(null);
    const [userDialog, setUserDialog] = useState(null);
    const [deleteUserDialog, setDeleteUserDialog] = useState(null);

    //Dialog actions
    const showUserDialog = (user) => {
        setUser(user)
        setUserDialog(true)
    }

    const closeUserDialog = () => {
        setUser(null);
        setUserDialog(false);
    }

    const showDeleteUserDialog = (user) => {
        setUser(user);
        setDeleteUserDialog(true);
    }

    const closeDeleteUserDialog = () => {
        setUser(null);
        setDeleteUserDialog(false);
    }

    return (
        <div className='user-administration'>

            {user && userDialog && props.locations !== null ? <UserDialog
                open={userDialog}
                close={closeUserDialog}
                addNewUser={props.addNewUser}
                updateUser={props.updateUser}
                showMessage={props.showMessage}
                user={user}
                currentUser={props.user}
                locations={props.locations}
                company={props.company}
                hospitals={props.hospitals}
                stations={props.stations}
            /> : null}

            <YesCancelDialog
                open={deleteUserDialog}
                close={closeDeleteUserDialog}
                header='Löschen'
                text={'Wollen Sie den Benutzer wirklich löschen?'}
                onClick={() => props.deleteUser(user)}
            />
            <div className='item-list'>
                {props.users.map(user => (
                    props.user?.role === "ADMIN" || (props.user?.role === "HOSPITAL_ADMIN" && props.user?.hospital.id === user.hospital?.id) ? <UserItem
                        key={user.id}
                        user={user}
                        showUserDialog={showUserDialog}
                        deleteUser={showDeleteUserDialog}
                        locations={props.locations}
                        showMessage={props.showMessage}
                        logout={props.logout}
                        setLoading={props.setLoading}
                    /> : null
                ))}
            </div>
            <ToolFab className='add-fab' onClick={() => showUserDialog({hospital: props.user.role === "HOSPITAL_ADMIN" ? props.user.hospital : null})} aria-label="add">
                <AddIcon/>
            </ToolFab>
        </div>
    )
}

export default UserAdministration;