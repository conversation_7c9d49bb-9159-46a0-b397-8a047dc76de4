import React, { useState, useEffect } from 'react';
import './UserItem.css';

// Components
import DeleteIcon from '@mui/icons-material/Delete';
import ArticleIcon from '@mui/icons-material/Article';
import ToolPaper from "../../../../../../../../reusable/ToolPaper/ToolPaper";

const UserItem = (props) => {

    return (
        <ToolPaper elevation={3} className='user-item'>

            <DeleteIcon
                onClick={() => props.deleteUser(props.user)}
                className='icon delete-icon'
            />
            <ArticleIcon
                onClick={() => props.showUserDialog(props.user)}
                className='icon edit-icon'
            />

            <ul className="information-holder">
                <li className="option-li">{props?.user?.username}</li>
            </ul>
        </ToolPaper>
    );
};

export default UserItem;