import React from "react";
import './DataTransferFieldItem.css';
import ToolPaper from "../../../../../../../../../reusable/ToolPaper/ToolPaper";

//Components
import DeleteIcon from '@mui/icons-material/Delete';

const keyLabels = {
    id: "Identifikationsnummer",
    nr: "Artikelnummer",
    name: "Artikelname",
    refNr: "Referenznummer",
    pcn: "PZN",
    ean: "EAN",
    purchaseNetPrice: "Einkaufspreis (netto)",
    salesNetPrice: "Verkaufspreis (netto)",
    articleGroupName: "Warengruppe",
    dimensions: "Größe",
    defaultSupplierName: "Standardlieferant"
};

const DataTransferFieldItem = (props) => {
    const { key: fieldKey, value } = props.dataTransferField || {};
    const label = keyLabels[fieldKey] || fieldKey;

    return (
        <ToolPaper elevation={3} className='data-transfer-field-item'>
            <DeleteIcon
                onClick={() => props.deleteDataTransferFieldOnClickHandler(props.dataTransferField)}
                className="icon delete-icon"
            />
            <p>{label}</p>
            <p>{value}</p>
        </ToolPaper>
    );
};

export default DataTransferFieldItem;
