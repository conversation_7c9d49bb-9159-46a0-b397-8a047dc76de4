import React, { useState, useEffect} from "react";
import './FileUploadDialog.css';
import Dialog from "@mui/material/Dialog";
import api from "../../../../../../../../api";

//Components

const FileUploadDialog = (props) => {

    const [hovered,setHovered] = useState(false)
    const [multipartFile,setMultipartFile] = useState(null)

    const onDragOverHandler = (e) => {
        e.preventDefault();
    }

    const allowedTypes = [
        "application/pdf",
        "text/plain",
        "application/msword", // .doc
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
        "application/vnd.ms-excel", // .xls
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" // .xlsx
    ];

    const onDropHandler = (e) => {
        e.preventDefault();
        let file = e.dataTransfer.files[0];
        if (!allowedTypes.includes(file.type)) {
            props.showMessage(2,"Ungültiger Dateityp: " + file.type);
        }else if(file.name.length > 127) {
            props.showMessage(2,"Der Dateiname darf maximal 127 Zeichen lang sein")
        }else if(file.size > 10000000){
            props.showMessage(2,"Die Datei darf maximal 10MB groß sein")
        }else{
            uploadFile(file)
        }
        setHovered(false)
    }

    const uploadFile = (file) => {
        let formData = new FormData();
        formData.append("multipartFile",file);
        props.setLoading(true);
        api.post("/data-settings/file",formData)
            .then(response => {
                props.addDataSettingToList(response.data)
                props.showMessage(0,'Datei erfolgreich hochgeladen. Sie wird nun verarbeitet');
                props.setLoading(false);
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
                props.setLoading(false);
            });
    }

    const onDragEnterHandler = () => {
        setHovered(true)
    }

    const onDragLeaveHandler = () => {
        setHovered(false)
    }

    let style = null;

    if(hovered) {
        style = {
            backgroundColor:'gray'
        }
    }else {
        style = {
            backgroundColor:'white'
        }
    }

    return (
        <Dialog open={props.open} onClose={props.close} className='file-upload-dialog'>
            <div
                onDragOver={onDragOverHandler}
                onDrop={onDropHandler}
                onDragEnter={onDragEnterHandler}
                onDragLeave={onDragLeaveHandler}
                className='surrounder'
                style={style}
            >
                <h3>Ziehen Sie eine Datei in das Feld</h3>
                <p className="import-info">
                    Erlaubte Formate: .pdf, .txt, .doc, .docx, .xlsx, .xls
                </p>
            </div>
        </Dialog>
    )
}

export default FileUploadDialog;