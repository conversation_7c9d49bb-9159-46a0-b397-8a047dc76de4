import React, {Component} from 'react';
import './CompanyAdministration.css';

//Components
import ToolButton from "../../../../../../../reusable/ToolButton/ToolButton";
import ClearIcon from '@mui/icons-material/Clear';
import MenuItem from "@mui/material/MenuItem";
import HelperFunctions from "../../../../../../../reusable/HelperFunctions";
import PostboxDialog from "./PostboxDialog/PostboxDialog";
import api from "../../../../../../../api";
import ToolTextField from "../../../../../../../reusable/ToolField/ToolTextField";

class CompanyAdministration extends Component {

    state = {
        company:this.props.company,
        accounts:this.props.accounts,
        showPostboxDialog:false
    }

    componentDidMount() {
        this.loadCompany();
    }

    loadCompany = () => {
        let url = "/company"
        api.get(url)
            .then(response => {
                this.setState({company:response.data});
            })
            .catch(error => {

            });
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        if(this.props !== prevProps) {
            this.setState({company:this.props.company,accounts:this.props.accounts});
        }
    }

    companyNameOnChangeHandler = (e) => {
        let company = this.state.company;
        company.name = e.target.value;
        this.setState({company:company});
    }

    logoOnChangeHandler = (e) => {
        let file = e.target.files[0];
        if(file.size < 1048576) {
            this.fileToDataUri(file)
                .then(async dataUri => {
                        let company = this.state.company;
                        company.logo = dataUri;
                        const blob = HelperFunctions.base64ToBlob(this.props.company.logo);
                        try {
                            const dimensions = await HelperFunctions.getImageDimensionsFromBlob(
                                blob
                            );
                            company.logoWidth = dimensions.width;
                            company.logoHeight = dimensions.height;
                        } catch (error) {
                            this.props.showMessage(2, "Etwas ist schiefgelaufen");
                            return;
                        }
                        this.setState({company: company});
                    }
                );
        }else{
            this.props.showMessage(2,"Das Logo darf maximal 1MB groß sein");
        }
    }

    fileToDataUri = (file) => new Promise(resolve => {
        const fileReader = new FileReader();
        fileReader.onload = (e) => {
            resolve(e.target.result);
        };
        fileReader.readAsDataURL(file);
    })

    deleteLogoOnClickHandler = () => {
        let company = this.state.company;
        company.logo = null;
        company.logoWidth = null;
        company.logoHeight = null;
        this.setState({company:company});
    }

    saveCompanyOnClickHandler = () => {
        this.props.updateCompany(this.state.company);
    }

    closePostboxDialog = () => {
        this.setState({showPostboxDialog:false});
    }

    setSystemEmail = (systemEmail) => {
        let company = this.state.company;
        company.systemEmail = systemEmail;
        this.setState({company:company});
    }

    render() {
        return (
            <div className='company-administration'>

                <PostboxDialog
                    showMessage={this.props.showMessage}
                    open={this.state.showPostboxDialog}
                    close={this.closePostboxDialog}
                    company={this.state.company}
                    setSystemEmail={this.setSystemEmail}
                    setLoading={this.props.setLoading}
                    logout={this.props.logout}
                />
                {this.state.company ? <div className='surrounder'>
                        <h2>Allgemein</h2>
                        <ToolTextField size="small" value={this.state.company ? this.state.company.name : ''} onChange={this.companyNameOnChangeHandler} label='Name' className='input'/>
                        <input className='input file-selector' type="file"  accept='image/*' onChange={this.logoOnChangeHandler} />
                        {this.state.company.logo ? <img className='logo-preview' src={this.state.company.logo} alt="Company Logo"/> : null}
                        {this.state.company.logo ? <ClearIcon onClick={this.deleteLogoOnClickHandler} className='clear-icon'/> : null}

                    </div> :
                    <h2>Lädt...</h2>}
                <ToolButton onClick={this.saveCompanyOnClickHandler} main className='save-button'>Speichern</ToolButton>
            </div>
        )
    }
}

export default CompanyAdministration;