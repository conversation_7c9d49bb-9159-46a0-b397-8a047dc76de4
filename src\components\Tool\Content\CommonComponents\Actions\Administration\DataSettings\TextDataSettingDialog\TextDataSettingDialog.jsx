import React, { useState, useEffect} from 'react';
import './TextDataSettingDialog.css';

//Components
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import ToolButton from "../../../../../../../../reusable/ToolButton/ToolButton";
import {Tooltip} from "recharts";
import ToolTextField from "../../../../../../../../reusable/ToolField/ToolTextField";
import api from "../../../../../../../../api";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";

const TextDataSettingDialog = (props) => {

    const [dataSetting,setDataSetting] = useState(props.user)

    useEffect(() => {
        setDataSetting(props.dataSetting)
    }, [props.dataSetting]);

    const saveDataSettingOnClickHandler = () => {
        if(validateDataSetting()) {
            if(dataSetting && dataSetting.id) {
                updateDataSetting(dataSetting)
            }else{
                createDataSetting(dataSetting);
            }
        }else{
            props.showMessage(2,"Fehlende oder falsche Werte")
        }
    }

    const createDataSetting = () => {
        console.log(dataSetting)
        api.post("/data-settings",dataSetting)
            .then(response => {
                props.addDataSettingToList(response.data)
                setDataSetting(response.data)
                props.showMessage(0,"Die Dateneinstellung wurde erfolgreich gespeichert");
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.setLoading(false);
                }
                props.setLoading(false);
            });
    }

    const updateDataSetting = () => {
        console.log(dataSetting)
        api.put("/data-settings",dataSetting)
            .then(response => {
                props.updateDataSettingInList(response.data)
                setDataSetting(response.data)
                props.showMessage(0,"Die Änderungen wurden erfolgreich gespeichert");
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.setLoading(false);
                }
                props.setLoading(false);
            });
    }

    const validateDataSetting = () => {
        if(dataSetting.name && dataSetting.text) {
            return true
        }else{
            return false
        }
    }

    const nameOnChangeHandler = (e) => {
        const updatedDataSetting = { ...dataSetting }
        updatedDataSetting.name = e.target.value
        setDataSetting(updatedDataSetting)
    }

    const textOnChangeHandler = (e) => {
        const updatedDataSetting = { ...dataSetting }
        updatedDataSetting.text = e.target.value
        setDataSetting(updatedDataSetting)
    }

    return (
        <Dialog scroll='body' size="l" fullWidth type={props.type} open={props.open} onClose={props.close} className='text-data-setting-dialog'>

            <Tooltip id="text-data-setting-dialog-tooltip"/>

            <DialogTitle className='title'>Dateneinstellung {dataSetting?.id ? "bearbeiten" : "anlegen"}</DialogTitle>
            <DialogContent>
                <ToolTextField size="small" value={dataSetting ? dataSetting.name : ""} onChange={nameOnChangeHandler}
                               className='field' label='Name'/>
                <ToolTextField multiline inputProps={{maxLength:5000}} minRows={5} maxRows={20} size="small" value={dataSetting ? dataSetting.text : ""} onChange={textOnChangeHandler}
                               className='textarea' label='Text'/>
            </DialogContent>
            <DialogActions>
                <ToolButton main onClick={saveDataSettingOnClickHandler} className='button'>Speichern</ToolButton>
            </DialogActions>
        </Dialog>
    )
}

export default TextDataSettingDialog;