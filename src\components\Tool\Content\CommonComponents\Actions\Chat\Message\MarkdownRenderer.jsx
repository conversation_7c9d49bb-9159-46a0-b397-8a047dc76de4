import React, { useState } from 'react';
import Markdown from 'markdown-to-jsx';
import { Prism as Synta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import DOMPurify from 'dompurify';
import './MarkdownRenderer.css';

const MarkdownRenderer = ({ content, className }) => {
    const [copiedStates, setCopiedStates] = useState({});

    // Preprocess markdown content for better compatibility
    const preprocessContent = (text) => {
        if (!text) return '';

        // Sanitize the input text first
        const sanitizedText = DOMPurify.sanitize(text, { USE_PROFILES: { html: false } });
        
        let processed = sanitizedText
            .replace(/^(.+)\n={3,}$/gm, '# $1')  // Convert === underlines to H1
            .replace(/^(.+)\n-{3,}$/gm, '## $1') // Convert --- underlines to H2
            .replace(/\n{3,}/g, '\n\n');         // Normalize excessive newlines

        // Safer URL auto-linking with proper escaping
        processed = processed.replace(
            /(?<!\]\(|`|<)(https?:\/\/[^\s<>`"']+)(?![\s]*\)|`|>)/g,
            (match) => {
                const safeUrl = DOMPurify.sanitize(match);
                return `<${safeUrl}>`;
            }
        );

        return processed;
    };

    // Handle copy to clipboard functionality
    const copyToClipboard = async (text, blockId) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopiedStates(prev => ({ ...prev, [blockId]: true }));
            setTimeout(() => {
                setCopiedStates(prev => ({ ...prev, [blockId]: false }));
            }, 2000);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    // Custom code component with syntax highlighting and copy functionality
    const CodeComponent = ({ className, children, ...props }) => {
        const codeText = String(children).replace(/\n$/, '');
        const blockId = Math.random().toString(36).substring(2, 11);

        // Determine if this is a code block or inline code
        const isCodeBlock = className && (
            className.includes('lang-') ||
            className.startsWith('language-') ||
            props.parent?.type === 'pre' ||
            codeText.includes('\n')
        );

        // Extract language from className
        let language = 'text';
        if (className) {
            if (className.includes('lang-')) {
                language = className.replace('lang-', '');
            } else if (className.includes('language-')) {
                language = className.replace('language-', '');
            }
        }

        // Render inline code
        if (!isCodeBlock) {
            return (
                <code className="markdown-inline-code" {...props}>
                    {children}
                </code>
            );
        }

        // Render code block with header and copy button
        return (
            <div className="markdown-code-wrapper">
                <div className="markdown-code-header">
                    <span className="markdown-code-language">{language}</span>
                    <button
                        className="markdown-copy-button"
                        onClick={() => copyToClipboard(codeText, blockId)}
                        title="Copy to clipboard"
                    >
                        {copiedStates[blockId] ? '✓ Copied' : '📋 Copy'}
                    </button>
                </div>
                <SyntaxHighlighter
                    style={oneDark}
                    language={language}
                    PreTag="div"
                    className="markdown-code-block"
                    {...props}
                >
                    {codeText}
                </SyntaxHighlighter>
            </div>
        );
    };

    // Handle plain code blocks without language specification
    const PreComponent = ({ children, ...props }) => {
        const codeChild = React.Children.only(children);

        if (codeChild && codeChild.type === 'code') {
            const codeText = String(codeChild.props.children).replace(/\n$/, '');
            const blockId = Math.random().toString(36).substring(2, 11);

            return (
                <div className="markdown-code-wrapper">
                    <div className="markdown-code-header">
                        <span className="markdown-code-language">text</span>
                        <button
                            className="markdown-copy-button"
                            onClick={() => copyToClipboard(codeText, blockId)}
                            title="Copy to clipboard"
                        >
                            {copiedStates[blockId] ? '✓ Copied' : '📋 Copy'}
                        </button>
                    </div>
                    <pre className="markdown-code-block-plain" {...props}>
                        {children}
                    </pre>
                </div>
            );
        }

        return <pre {...props}>{children}</pre>;
    };

    // Custom link component with validation and security
    const LinkComponent = ({ href, children, ...props }) => {
        const isValidUrl = (url) => {
            try {
                const urlObj = new URL(url);
                return ['http:', 'https:', 'mailto:'].includes(urlObj.protocol);
            } catch {
                return false;
            }
        };

        if (!isValidUrl(href)) {
            return <span className="markdown-invalid-link">{children}</span>;
        }

        return (
            <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="markdown-link"
                {...props}
            >
                {children}
            </a>
        );
    };

    // Table components with dark theme styling
    const TableComponent = ({ children, ...props }) => (
        <div className="markdown-table-wrapper" style={{
            margin: '12px 0',
            overflowX: 'auto',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            border: '2px solid #4a5568',
            backgroundColor: '#2d3748'
        }}>
            <table className="markdown-table" style={{
                width: '100%',
                borderCollapse: 'collapse',
                fontSize: '14px',
                backgroundColor: '#2d3748',
                margin: '0',
                color: '#e2e8f0'
            }} {...props}>
                {children}
            </table>
        </div>
    );

    const TableHeaderComponent = ({ children, ...props }) => (
        <th style={{
            padding: '14px 18px',
            textAlign: 'left',
            borderBottom: '3px solid #4D79FF',
            borderRight: '2px solid #4a5568',
            backgroundColor: '#1a365d',
            fontWeight: '700',
            color: '#ffffff',
            fontSize: '14px',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            boxShadow: 'inset 0 -2px 0 #4D79FF'
        }} {...props}>
            {children}
        </th>
    );

    const TableCellComponent = ({ children, ...props }) => (
        <td style={{
            padding: '12px 16px',
            textAlign: 'left',
            borderBottom: '2px solid #4a5568',
            borderRight: '2px solid #4a5568',
            color: '#e2e8f0',
            backgroundColor: 'inherit'
        }} {...props}>
            {children}
        </td>
    );

    const TableRowComponent = ({ children, ...props }) => (
        <tr style={{
            backgroundColor: 'inherit'
        }} {...props}>
            {children}
        </tr>
    );

    // Blockquote component with line break preservation
    const BlockquoteComponent = ({ children, ...props }) => (
        <blockquote className="markdown-blockquote" style={{
            margin: '12px 0',
            padding: '12px 16px',
            borderLeft: '4px solid #4D79FF',
            backgroundColor: '#f5f5f5',
            fontStyle: 'italic',
            color: '#555',
            borderRadius: '0 4px 4px 0',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            whiteSpace: 'pre-wrap',
            lineHeight: '1.6'
        }} {...props}>
            {children}
        </blockquote>
    );

    // Configure markdown-to-jsx with custom components
    const options = {
        disableParsingRawHTML: true,
        overrides: {
            code: CodeComponent,
            pre: PreComponent,
            a: LinkComponent,
            table: TableComponent,
            th: TableHeaderComponent,
            td: TableCellComponent,
            tr: TableRowComponent,
            blockquote: BlockquoteComponent,
            h1: { component: 'h1', props: { className: 'markdown-heading markdown-h1' } },
            h2: { component: 'h2', props: { className: 'markdown-heading markdown-h2' } },
            h3: { component: 'h3', props: { className: 'markdown-heading markdown-h3' } },
            h4: { component: 'h4', props: { className: 'markdown-heading markdown-h4' } },
            h5: { component: 'h5', props: { className: 'markdown-heading markdown-h5' } },
            h6: { component: 'h6', props: { className: 'markdown-heading markdown-h6' } },
            p: { component: 'p', props: { className: 'markdown-paragraph' } },
            ul: { component: 'ul', props: { className: 'markdown-list markdown-unordered-list' } },
            ol: { component: 'ol', props: { className: 'markdown-list markdown-ordered-list' } },
            li: { component: 'li', props: { className: 'markdown-list-item' } },
            strong: { component: 'strong', props: { className: 'markdown-strong' } },
            em: { component: 'em', props: { className: 'markdown-emphasis' } },
            hr: { component: 'hr', props: { className: 'markdown-hr' } },
        }
    };

    return (
        <div className={`markdown-renderer ${className || ''}`}>
            <Markdown options={options}>
                {preprocessContent(content || '')}
            </Markdown>
        </div>
    );
};

export default MarkdownRenderer;
