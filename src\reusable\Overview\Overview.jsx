import React, {Component} from 'react';
import './Overview.css';

//Components
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import OverviewItem from "./OverviewItem/OverviewItem";
import Fab from "@mui/material/Fab";
import AddIcon from "@mui/icons-material/Add";

class Overview extends Component {
    render() {

        const search = [

        ]

        return (
            <div className='overview'>
                <div className='search-bar'>
                    <h1>Bestellübersicht</h1>
                    <TextField
                        className='search-input'
                        label='Suchwort'
                    />
                    <TextField
                        className='search-select'
                        select
                        label="Select"
                        value={0}
                        helperText='Suche auswählen'
                    >
                        {search.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                                {option.label}
                            </MenuItem>
                        ))}
                    </TextField>
                    <Button className='search-button' variant='contained' color='primary'>Suche</Button>
                </div>
                <div className='overview-items-panel'>
                    {this.props.overviewItems.map((overviewItem,index) => (
                        <OverviewItem
                            key={index}
                            overviewItem={overviewItem}
                        />
                    ))}
                </div>
                <Fab className='add-fab' color="primary" aria-label="add">
                    <AddIcon />
                </Fab>
            </div>
        )
    }
}

export default Overview;