import React, { useState, useEffect } from 'react';
import './TripItem.css';

// Components
import DeleteIcon from '@mui/icons-material/Delete';
import ArticleIcon from '@mui/icons-material/Article';
import HelperFunctions from "../HelperFunctions";
import ToolPaper from "../ToolPaper/ToolPaper";
import {Tooltip} from "react-tooltip";

const TripItem = (props) => {

    const _handleDoubleClick = () => {
        if(props.showTripDialog) {
            props.showTripDialog(props.trip);
        }
    }

    return (
        <ToolPaper onDoubleClick={_handleDoubleClick} elevation={3} className={`trip-item ${props.sidepanel ? "small-trip-item" : ""} ${!props.sidepanel && props.type !== "completed-trip" && props.user && (((props.user.role === "USER" || props.user.role === "ADMIN") && !props.trip.openedMaster) || ((props.user.role === "HOSPITAL_STAFF" || props.user.role === "HOSPITAL_ADMIN") && !props.trip.openedCustomer)) ? 'small-pulse' : ''}`}>
            <Tooltip id="trip-item-tooltip"/>
            {props.deletable ? <DeleteIcon
                onClick={() => props.showDeleteTripDialog(props.trip)}
                className='icon delete-icon'
            /> : null}

            <ArticleIcon
                onClick={() => props.showTripDialog(props.trip)}
                className='icon edit-icon'
            />

            {props.type === "trip-request" && props.trip && props.trip.changeRequest ? <div data-tooltip-id="trip-management-tooltip" data-tooltip-place="top" data-tooltip-content="Laufende Änderungsanfrage" style={{background:"#FFB900"}} className='status'/> : null}

            <ul className="information-holder">

                <li className="option-li">{props.trip?.patient?.firstName ? props.trip.patient.firstName : null}</li>
                <li className="option-li">{props.trip?.patient?.lastName ? props.trip.patient.lastName : null}</li>

                {props.trip && !props.trip.tripSeries ?
                    <li className="option-li">{HelperFunctions.formatDate(props.trip.pickUpTime)}</li> : null}

                {props.trip && props.trip.tripSeries ?
                    <li className="option-li">
                        {props.trip.monday || props.trip.returnTripMonday ?
                            <li className="multiple-holder-li"><p>Mo</p></li> : null}
                        {props.trip.tuesday || props.trip.returnTripTuesday ?
                            <li className="multiple-holder-li"><p>Di</p></li> : null}
                        {props.trip.wednesday || props.trip.returnTripWednesday ?
                            <li className="multiple-holder-li"><p>Mi</p></li> : null}
                        {props.trip.thursday || props.trip.returnTripThursday ?
                            <li className="multiple-holder-li"><p>Do</p></li> : null}
                        {props.trip.friday || props.trip.returnTripFriday ?
                            <li className="multiple-holder-li"><p>Fr</p></li> : null}
                        {props.trip.saturday || props.trip.returnTripSaturday ?
                            <li className="multiple-holder-li"><p>Sa</p></li> : null}
                        {props.trip.sunday || props.trip.returnTripSunday ?
                            <li className="multiple-holder-li"><p>So</p></li> : null}
                    </li> : null}
            </ul>
            {props.user && (props.user.role === "ADMIN" || props.user.role === "USER") ?
                <p className="small-info">Angefragt von {props.trip?.hospital?.name}</p> : null}
        </ToolPaper>
    );
};

export default TripItem;