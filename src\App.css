.app {
    height: 100%;
    width: 100%;
    background-color: var(--background-primary);
    color: var(--text-primary);
}

html, body, #root {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden; /* optional, but helps with weird scroll issues */
    position: relative;
    background-color: var(--background-primary);
    color: var(--text-primary);
}

.app-content {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
}