import React, {useEffect, useState} from "react";
import './TripDialog.css';

//Components
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import ToolTextField from "../ToolField/ToolTextField";
import DialogActions from "@mui/material/DialogActions";
import CloseIcon from '@mui/icons-material/Close';
import DialogContent from "@mui/material/DialogContent";
import Autocomplete, {createFilterOptions} from "@mui/material/Autocomplete";
import MenuItem from "@mui/material/MenuItem";
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import ChangeRequestDialog from "./ChangeRequestDialog/ChangeRequestDialog";
import ToolButton from "../ToolButton/ToolButton";
import YesCancelDialog from "../YesCancelDialog/YesCancelDialog";
import HelperFunctions from "../HelperFunctions";
import {Tooltip} from "react-tooltip";
import jsPDF from "jspdf";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";

const TripDialog = (props) => {

    const [trip,setTrip] = useState(null)
    const [today,setToday] = useState(null)
    const [validate,setValidate] = useState(false)

    // Dialog actions
    const [saveTripRequestDialog,setSaveTripRequestDialog] = useState(false)
    const [confirmTripRequestDialog,setConfirmTripRequestDialog] = useState(false)
    const [rejectTripRequestDialog,setRejectTripRequestDialog] = useState(false)
    const [completeTripDialog,setCompleteTripDialog] = useState(false)
    const [completeFirstTripOnlyDialog,setCompleteFirstTripOnlyDialog] = useState(false)
    const [changeRequestDialog,setChangeRequestDialog] = useState(false)
    const [confirmChangedTripRequestDialog,setConfirmChangedTripRequestDialog] = useState(false)
    const [rejectChangedTripRequestDialog,setRejectChangedTripRequestDialog] = useState(false)

    useEffect(() => {
        if(props.trip !== trip) {
            setTrip(props.trip)
            setValidate(false)
        }
    }, [props]);

    const patientOnChangeHandler = (patient) => {
        setTrip({ ...trip, patient })
    }

    const patientFirstNameOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        if(updatedTrip.patient) {
            updatedTrip.patient.firstName = e.target.value
        }else{
            updatedTrip.patient = {
                firstName: e.target.value
            }
        }
        setTrip(updatedTrip)
    }

    const patientLastNameOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        if(updatedTrip.patient) {
            updatedTrip.patient.lastName = e.target.value
        }else{
            updatedTrip.patient = {
                lastName: e.target.value
            }
        }
        setTrip(updatedTrip)
    }

    const patientBirthdateOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        if(updatedTrip.patient) {
            updatedTrip.patient.birthdate = e.target.value
        }else{
            updatedTrip.patient = {
                birthdate: e.target.value
            }
        }
        setTrip(updatedTrip)
    }

    const patientStationOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        if(updatedTrip.patient) {
            updatedTrip.patient.station = props.stations.find(station => station.id === e.target.value)
        }else{
            updatedTrip.patient = {
                station: props.stations.find(station => station.id === e.target.value)
            }
        }
        setTrip(updatedTrip)
    }

    const toggleTripSeries = () => {
        setTrip({ ...trip, tripSeries: !trip.tripSeries })
    }

    const startDateOnChangeHandler = (e) => {
        setTrip({ ...trip, startDate: e.target.value })
    }

    const endDateOnChangeHandler = (e) => {
        setTrip({ ...trip, endDate: e.target.value })
    }

    const toggleMonday = () => {
        setTrip({ ...trip, monday: !trip.monday })
    }

    const mondayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, mondayTime: e.target.value })
    }

    const toggleTuesday = () => {
        setTrip({ ...trip, tuesday: !trip.tuesday })
    }

    const tuesdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, tuesdayTime: e.target.value })
    }

    const toggleWednesday = () => {
        setTrip({ ...trip, wednesday: !trip.wednesday })
    }

    const wednesdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, wednesdayTime: e.target.value })
    }

    const toggleThursday = () => {
        setTrip({ ...trip, thursday: !trip.thursday })
    }

    const thursdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, thursdayTime: e.target.value })
    }

    const toggleFriday = () => {
        setTrip({ ...trip, friday: !trip.friday })
    }

    const fridayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, fridayTime: e.target.value })
    }

    const toggleSaturday = () => {
        setTrip({ ...trip, saturday: !trip.saturday })
    }

    const saturdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, saturdayTime: e.target.value })
    }

    const toggleSunday = () => {
        setTrip({ ...trip, sunday: !trip.sunday })
    }

    const sundayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, sundayTime: e.target.value })
    }

    const pickUpTimeOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.pickUpTime = e.target.value;
        if(e.target.value) {
            let hours = e.target.value.substring(11,13);
            hours = parseInt(hours) + 1;
            if(hours < 24) {
                if(hours < 10) {
                    hours = "0" + hours;
                }
                updatedTrip.pickUpTimeRange = e.target.value.substring(0,11) + hours + e.target.value.substring(13,16)
            }
        }
        setTrip(updatedTrip)
    }

    const pickUpTimeRangeOnChangeHandler = (e) => {
        setTrip({ ...trip, pickUpTimeRange: e.target.value })
    }

    const changeRequestPickUpTimeOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.changeRequestPickUpTime = e.target.value;
        if(e.target.value) {
            let hours = e.target.value.substring(11,13);
            hours = parseInt(hours) + 1;
            if(hours < 24) {
                if(hours < 10) {
                    hours = "0" + hours;
                }
                let formattedDateTime = e.target.value.substring(0,11) + hours + e.target.value.substring(13,16);
                updatedTrip.changeRequestPickUpTimeRange = formattedDateTime;
            }
        }
        setTrip(updatedTrip)
    }

    const changeRequestPickUpTimeRangeOnChangeHandler = (e) => {
        setTrip({ ...trip, changeRequestPickUpTimeRange: e.target.value })
    }

    const changeRequestEndDateOnChangeHandler = (e) => {
        setTrip({ ...trip, changeRequestEndDate: e.target.value })
    }

    const locationNameFromOnChangeHandler = (value) => {
        let updatedTrip = { ...trip }
        updatedTrip.locationNameFrom = value;
        if(value === "Krankenhaus" || value === "Praxis") {
            if(!updatedTrip.stationFrom) {
                if(updatedTrip.patient?.station) {
                    updatedTrip.stationFrom = updatedTrip.patient.station
                }else if(props.user.station) {
                    updatedTrip.stationFrom = props.user.station
                }
            }
            if(!updatedTrip.streetNameFrom && props.user?.hospital?.address?.streetName) {
                updatedTrip.streetNameFrom = props.user.hospital.address.streetName
            }
            if(!updatedTrip.streetNrFrom && props.user?.hospital?.address?.streetNr) {
                updatedTrip.streetNrFrom = props.user.hospital.address.streetNr
            }
            if(!updatedTrip.addressAdditionFrom && props.user?.hospital?.address?.supplement) {
                updatedTrip.addressAdditionFrom = props.user.hospital.address.supplement
            }
            if(!updatedTrip.postCodeFrom && props.user?.hospital?.address?.postalCode) {
                updatedTrip.postCodeFrom = props.user.hospital.address.postalCode
            }
            if(!updatedTrip.cityFrom && props.user?.hospital?.address?.city) {
                updatedTrip.cityFrom = props.user.hospital.address.city
            }
        }
        setTrip(updatedTrip)
    }

    const stationFromOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.stationFrom = props.stations.find(station => station.id === e.target.value)
        setTrip(updatedTrip)
    }

    const streetNameFromOnChangeHandler = (e) => {
        setTrip({ ...trip, streetNameFrom: e.target.value })
    }

    const streetNrFromOnChangeHandler = (e) => {
        setTrip({ ...trip, streetNrFrom: e.target.value })
    }

    const addressAdditionFromOnChangeHandler = (e) => {
        setTrip({ ...trip, addressAdditionFrom: e.target.value })
    }

    const postCodeFromOnChangeHandler = (e) => {
        setTrip({ ...trip, postCodeFrom: e.target.value })
    }

    const cityFromOnChangeHandler = (e) => {
        setTrip({ ...trip, cityFrom: e.target.value })
    }

    const countryFromOnChangeHandler = (e) => {
        setTrip({ ...trip, countryFrom: e.target.value })
    }

    const arrivalTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, arrivalTime: e.target.value })
    }

    const locationNameToOnChangeHandler = (value) => {
        let updatedTrip = { ...trip }
        updatedTrip.locationNameTo = value;
        if(value === "Krankenhaus" || value === "Praxis") {
            if(!updatedTrip.stationTo) {
                if(updatedTrip.patient?.station) {
                    updatedTrip.stationTo = updatedTrip.patient.station
                }else if(props.user.station) {
                    updatedTrip.stationTo = props.user.station
                }
            }
            if(!updatedTrip.streetNameTo && props.user?.hospital?.address?.streetName) {
                updatedTrip.streetNameTo = props.user.hospital.address.streetName
            }
            if(!updatedTrip.streetNrTo && props.user?.hospital?.address?.streetNr) {
                updatedTrip.streetNrTo = props.user.hospital.address.streetNr
            }
            if(!updatedTrip.addressAdditionTo && props.user?.hospital?.address?.supplement) {
                updatedTrip.addressAdditionTo = props.user.hospital.address.supplement
            }
            if(!updatedTrip.postCodeTo && props.user?.hospital?.address?.postalCode) {
                updatedTrip.postCodeTo = props.user.hospital.address.postalCode
            }
            if(!updatedTrip.cityTo && props.user?.hospital?.address?.city) {
                updatedTrip.cityTo = props.user.hospital.address.city
            }
        }
        setTrip(updatedTrip)
    }

    const stationToOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.stationTo = props.stations.find(station => station.id === e.target.value)
        setTrip(updatedTrip)
    }

    const streetNameToOnChangeHandler = (e) => {
        setTrip({ ...trip, streetNameTo: e.target.value })
    }

    const streetNrToOnChangeHandler = (e) => {
        setTrip({ ...trip, streetNrTo: e.target.value })
    }

    const addressAdditionToOnChangeHandler = (e) => {
        setTrip({ ...trip, addressAdditionTo: e.target.value })
    }

    const postCodeToOnChangeHandler = (e) => {
        setTrip({ ...trip, postCodeTo: e.target.value })
    }

    const cityToOnChangeHandler = (e) => {
        setTrip({ ...trip, cityTo: e.target.value })
    }

    const countryToOnChangeHandler = (e) => {
        setTrip({ ...trip, countryTo: e.target.value })
    }

    const toggleCompanion = () => {
        setTrip({ ...trip, companion: !trip.companion })
    }

    const toggleInfection = () => {
        setTrip({ ...trip, infection: !trip.infection })
    }

    const costUnitOnChangeHandler = (e) => {
        setTrip({ ...trip, costUnit: e.target.value })
    }

    const transportKindOnChangeHandler = (e) => {
        setTrip({ ...trip, transportKind: e.target.value })
    }

    const tripTypeOnChangeHandler = (e) => {
        setTrip({ ...trip, tripType: e.target.value })
    }

    const commentOnChangeHandler = (e) => {
        setTrip({ ...trip, comment: e.target.value })
    }

    // Return Trip

    const toggleReturnTrip = () => {
        let updatedTrip = { ...trip }
        if(!updatedTrip.returnTrip) {
            updatedTrip.returnTripLocationNameFrom = updatedTrip.locationNameTo;
            updatedTrip.returnTripStationFrom = updatedTrip.stationTo;
            updatedTrip.returnTripStreetNameFrom = updatedTrip.streetNameTo;
            updatedTrip.returnTripStreetNrFrom = updatedTrip.streetNrTo;
            updatedTrip.returnTripAddressAdditionFrom = updatedTrip.addressAdditionTo;
            updatedTrip.returnTripPostCodeFrom = updatedTrip.postCodeTo;
            updatedTrip.returnTripCityFrom = updatedTrip.cityTo;
            updatedTrip.returnTripCountryFrom = updatedTrip.countryTo;
            updatedTrip.returnTripLocationNameTo = updatedTrip.locationNameFrom;
            updatedTrip.returnTripStreetNameTo = updatedTrip.streetNameFrom;
            updatedTrip.returnTripStreetNrTo = updatedTrip.streetNrFrom;
            updatedTrip.returnTripAddressAdditionTo = updatedTrip.addressAdditionFrom;
            updatedTrip.returnTripPostCodeTo = updatedTrip.postCodeFrom;
            updatedTrip.returnTripCityTo = updatedTrip.cityFrom;
            updatedTrip.returnTripCountryTo = updatedTrip.countryFrom;
            updatedTrip.returnTripTransportKind = updatedTrip.transportKind;
            updatedTrip.returnTripCompanion = updatedTrip.companion;
            updatedTrip.returnTripCostUnit = updatedTrip.costUnit;
            updatedTrip.returnTripInfection = updatedTrip.infection;
            updatedTrip.returnTripComment = updatedTrip.comment;
            if(updatedTrip.tripSeries) {
                updatedTrip.returnTripMonday = updatedTrip.monday;
                updatedTrip.returnTripTuesday = updatedTrip.tuesday;
                updatedTrip.returnTripWednesday = updatedTrip.wednesday;
                updatedTrip.returnTripThursday = updatedTrip.thursday;
                updatedTrip.returnTripFriday = updatedTrip.friday;
                updatedTrip.returnTripSaturday = updatedTrip.saturday;
                updatedTrip.returnTripSunday = updatedTrip.sunday;
                updatedTrip.returnTripMondayTime = updatedTrip.mondayTime;
                updatedTrip.returnTripTuesdayTime = updatedTrip.tuesdayTime;
                updatedTrip.returnTripWednesdayTime = updatedTrip.wednesdayTime;
                updatedTrip.returnTripThursdayTime = updatedTrip.thursdayTime;
                updatedTrip.returnTripFridayTime = updatedTrip.fridayTime;
                updatedTrip.returnTripSaturdayTime = updatedTrip.saturdayTime;
                updatedTrip.returnTripSundayTime = updatedTrip.sundayTime;
            }
        }else{
            updatedTrip.returnTripLocationNameFrom = null;
            updatedTrip.returnTripStationFrom = null;
            updatedTrip.returnTripStreetNameFrom = null;
            updatedTrip.returnTripStreetNrFrom = null;
            updatedTrip.returnTripAddressAdditionFrom = null;
            updatedTrip.returnTripPostCodeFrom = null;
            updatedTrip.returnTripCityFrom = null;
            updatedTrip.returnTripCountryFrom = null;
            updatedTrip.returnTripLocationNameTo = null;
            updatedTrip.returnTripStreetNameTo = null;
            updatedTrip.returnTripStreetNrTo = null;
            updatedTrip.returnTripAddressAdditionTo = null;
            updatedTrip.returnTripPostCodeTo = null;
            updatedTrip.returnTripCityTo = null;
            updatedTrip.returnTripCountryTo = null;
            updatedTrip.returnTripTransportKind = null;
            updatedTrip.returnTripCompanion = null;
            updatedTrip.returnTripCostUnit = null;
            updatedTrip.returnTripInfection = null;
            updatedTrip.returnTripComment = null;
            updatedTrip.returnTripMonday = null;
            updatedTrip.returnTripTuesday = null;
            updatedTrip.returnTripWednesday = null;
            updatedTrip.returnTripThursday = null;
            updatedTrip.returnTripFriday = null;
            updatedTrip.returnTripSaturday = null;
            updatedTrip.returnTripSunday = null;
            updatedTrip.returnTripMondayTime = null;
            updatedTrip.returnTripTuesdayTime = null;
            updatedTrip.returnTripWednesdayTime = null;
            updatedTrip.returnTripThursdayTime = null;
            updatedTrip.returnTripFridayTime = null;
            updatedTrip.returnTripSaturdayTime = null;
            updatedTrip.returnTripSundayTime = null;
        }
        updatedTrip.returnTrip = !updatedTrip.returnTrip;
        setTrip(updatedTrip)
    }

    const toggleReturnTripMonday = () => {
        setTrip({ ...trip, returnTripMonday: !trip.returnTripMonday })
    }

    const returnTripMondayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripMondayTime: e.target.value })
    }

    const toggleReturnTripTuesday = () => {
        setTrip({ ...trip, returnTripTuesday: !trip.returnTripTuesday })
    }

    const returnTripTuesdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripTuesdayTime: e.target.value })
    }

    const toggleReturnTripWednesday = () => {
        setTrip({ ...trip, returnTripWednesday: !trip.returnTripWednesday })
    }

    const returnTripWednesdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripWednesdayTime: e.target.value })
    }

    const toggleReturnTripThursday = () => {
        setTrip({ ...trip, returnTripThursday: !trip.returnTripThursday })
    }

    const returnTripThursdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripThursdayTime: e.target.value })
    }

    const toggleReturnTripFriday = () => {
        setTrip({ ...trip, returnTripFriday: !trip.returnTripFriday })
    }

    const returnTripFridayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripFridayTime: e.target.value })
    }

    const toggleReturnTripSaturday = () => {
        setTrip({ ...trip, returnTripSaturday: !trip.returnTripSaturday })
    }

    const returnTripSaturdayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripSaturdayTime: e.target.value })
    }

    const toggleReturnTripSunday = () => {
        setTrip({ ...trip, returnTripSunday: !trip.returnTripSunday })
    }

    const returnTripSundayTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripSundayTime: e.target.value })
    }

    const returnTripPickUpTimeOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.returnTripPickUpTime = e.target.value;
        if(e.target.value) {
            let hours = e.target.value.substring(11,13);
            hours = parseInt(hours) + 1;
            if(hours < 24) {
                if(hours < 10) {
                    hours = "0" + hours;
                }
                let formattedDateTime = e.target.value.substring(0,11) + hours + e.target.value.substring(13,16);
                updatedTrip.returnTripPickUpTimeRange = formattedDateTime;
            }
        }
        setTrip(updatedTrip)
    }

    const returnTripPickUpTimeRangeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripPickUpTimeRange: e.target.value })
    }

    const returnTripChangeRequestPickUpTimeOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.returnTripChangeRequestPickUpTime = e.target.value;
        if(e.target.value) {
            let hours = e.target.value.substring(11,13);
            hours = parseInt(hours) + 1;
            if(hours < 24) {
                if(hours < 10) {
                    hours = "0" + hours;
                }
                let formattedDateTime = e.target.value.substring(0,11) + hours + e.target.value.substring(13,16);
                updatedTrip.returnTripChangeRequestPickUpTimeRange = formattedDateTime;
            }
        }
        setTrip(updatedTrip)
    }

    const returnTripChangeRequestPickUpTimeRangeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripChangeRequestPickUpTimeRange: e.target.value })
    }

    const returnTripLocationNameFromOnChangeHandler = (value) => {
        let updatedTrip = { ...trip }
        updatedTrip.returnTripLocationNameFrom = value;
        if(value === "Krankenhaus" || value === "Praxis") {
            if(!updatedTrip.returnTripStationFrom) {
                if(updatedTrip.patient?.station) {
                    updatedTrip.returnTripStationFrom = updatedTrip.patient.station
                }else if(props.user.station) {
                    updatedTrip.returnTripStationFrom = props.user.station
                }
            }
            if(!updatedTrip.returnTripStreetNameFrom) {
                updatedTrip.returnTripStreetNameFrom = props.user?.hospital?.address?.streetName;
            }
            if(!updatedTrip.returnTripStreetNrFrom) {
                updatedTrip.returnTripStreetNrFrom = props.user?.hospital?.address?.streetNr;
            }
            if(!updatedTrip.returnTripAddressAdditionFrom) {
                updatedTrip.returnTripAddressAdditionFrom = props.user?.hospital?.address?.supplement;
            }
            if(!updatedTrip.returnTripPostCodeFrom) {
                updatedTrip.returnTripPostCodeFrom = props.user?.hospital?.address?.postalCode;
            }
            if(!updatedTrip.returnTripCityFrom) {
                updatedTrip.returnTripCityFrom = props.user?.hospital?.address?.city;
            }
        }
        setTrip(updatedTrip)
    }

    const returnTripStationFromOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.returnTripStationFrom = props.stations.find(station => station.id === e.target.value)
        setTrip(updatedTrip)
    }

    const returnTripStreetNameFromOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripStreetNameFrom: e.target.value })
    }

    const returnTripStreetNrFromOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripStreetNrFrom: e.target.value })
    }

    const returnTripAddressAdditionFromOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripAddressAdditionFrom: e.target.value })
    }

    const returnTripPostCodeFromOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripPostCodeFrom: e.target.value })
    }

    const returnTripCityFromOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripCityFrom: e.target.value })
    }

    const returnTripCountryFromOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripCountryFrom: e.target.value })
    }

    const returnTripArrivalTimeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripArrivalTime: e.target.value })
    }

    const returnTripLocationNameToOnChangeHandler = (value) => {
        let updatedTrip = { ...trip }
        updatedTrip.returnTripLocationNameTo = value;
        if(value === "Krankenhaus" || value === "Praxis") {
            if(!updatedTrip.returnTripStationTo) {
                if(updatedTrip.patient?.station) {
                    updatedTrip.returnTripStationTo = updatedTrip.patient.station
                }else if(props.user.station) {
                    updatedTrip.returnTripStationTo = props.user.station
                }
            }
            if(!updatedTrip.returnTripStreetNameTo && props.user?.hospital?.address?.streetName) {
                updatedTrip.returnTripStreetNameTo = props.user.hospital.address.streetName
            }
            if(!updatedTrip.returnTripStreetNrTo && props.user?.hospital?.address?.streetNr) {
                updatedTrip.returnTripStreetNrTo = props.user.hospital.address.streetNr
            }
            if(!updatedTrip.returnTripAddressAdditionTo && props.user?.hospital?.address?.supplement) {
                updatedTrip.returnTripAddressAdditionTo = props.user.hospital.address.supplement
            }
            if(!updatedTrip.returnTripPostCodeTo && props.user?.hospital?.address?.postalCode) {
                updatedTrip.returnTripPostCodeTo = props.user.hospital.address.postalCode
            }
            if(!updatedTrip.returnTripCityTo && props.user?.hospital?.address?.city) {
                updatedTrip.returnTripCityTo = props.user.hospital.address.city
            }
        }
        setTrip(updatedTrip)
    }

    const returnTripStationToOnChangeHandler = (e) => {
        let updatedTrip = { ...trip }
        updatedTrip.returnTripStationTo = props.stations.find(station => station.id === e.target.value)
        setTrip(updatedTrip)
    }

    const returnTripStreetNameToOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripStreetNameTo: e.target.value })
    }

    const returnTripStreetNrToOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripStreetNrTo: e.target.value })
    }

    const returnTripAddressAdditionToOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripAddressAdditionTo: e.target.value })
    }

    const returnTripPostCodeToOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripPostCodeTo: e.target.value })
    }

    const returnTripCityToOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripCityTo: e.target.value })
    }

    const returnTripCountryToOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripCountryTo: e.target.value })
    }

    const returnTripToggleCompanion = () => {
        setTrip({ ...trip, returnTripCompanion: !trip.returnTripCompanion })
    }

    const returnTripToggleInfection = () => {
        setTrip({ ...trip, returnTripInfection: !trip.returnTripInfection })
    }

    const returnTripCostUnitOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripCostUnit: e.target.value })
    }

    const returnTripTransportKindOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripTransportKind: e.target.value })
    }

    const returnTripTripTypeOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripTripType: e.target.value })
    }

    const returnTripCommentOnChangeHandler = (e) => {
        setTrip({ ...trip, returnTripComment: e.target.value })
    }

    const saveTripRequestOnClickHandler = (trip) => {
        if(!trip.patient || !trip.patient.firstName || !trip.patient.lastName || !trip.patient.birthdate || (!trip.tripSeries && !trip.pickUpTime)) {
        }
        if((trip.tripSeries) && (
            (!trip.monday && !trip.tuesday && !trip.wednesday &&! trip.thursday && !trip.friday && !trip.saturday && !trip.sunday) || (trip.monday && !trip.mondayTime) || (trip.tuesday && !trip.tuesdayTime) || (trip.wednesday && !trip.wednesdayTime) ||
            (trip.thursday && !trip.thursdayTime) || (trip.friday && !trip.fridayTime) || (trip.saturday && !trip.saturdayTime) || (trip.sunday && !trip.sundayTime))) {
        }
        if(!trip.locationNameFrom || (trip.locationNameFrom === "Krankenhaus" && !trip.stationFrom) || !trip.streetNameFrom || !trip.streetNrFrom || !trip.postCodeFrom || !trip.cityFrom ||
            !trip.countryFrom || !trip.locationNameTo || (trip.locationNameTo === "Krankenhaus" && !trip.stationTo) || !trip.streetNameTo || !trip.streetNrTo || !trip.postCodeTo ||
            !trip.cityTo || !trip.countryTo || (trip.costUnit === 2 && !trip.healthInsurance) || !trip.transportKind || !trip.tripType) {
        }
        if(trip.returnTrip &&
            (trip.tripSeries && (((!trip.returnTripMonday && !trip.returnTripTuesday && !trip.returnTripWednesday &&! trip.returnTripThursday && !trip.returnTripFriday && !trip.returnTripSaturday && !trip.returnTripSunday) ||
                    (trip.returnTripMonday && !trip.returnTripMondayTime) || (trip.returnTripTuesday && !trip.returnTripTuesdayTime) || (trip.returnTripWednesday && !trip.returnTripWednesdayTime) ||
                    (trip.returnTripThursday && !trip.returnTripThursdayTime) || (trip.returnTripFriday && !trip.returnTripFridayTime) || (trip.returnTripSaturday && !trip.returnTripSaturdayTime) ||
                    (trip.returnTripSunday && !trip.returnTripSundayTime)) ||
                (!trip.returnTripLocationNameFrom || (trip.returnTripLocationNameFrom === "Krankenhaus" && !trip.returnTripStationFrom) || !trip.returnTripStreetNameFrom || !trip.returnTripStreetNrFrom ||
                    !trip.returnTripPostCodeFrom || !trip.returnTripCityFrom || !trip.returnTripCountryFrom || !trip.returnTripLocationNameTo || (trip.returnTripLocationNameTo === "Krankenhaus" && !trip.returnTripStationTo) ||
                    !trip.returnTripStreetNameTo || !trip.returnTripStreetNrTo || !trip.returnTripPostCodeTo || !trip.returnTripCityTo || !trip.returnTripCountryTo || (trip.returnTripCostUnit === 2 && !trip.returnTripHealthInsurance) ||
                    !trip.returnTripTransportKind || !trip.returnTripTripType)))) {
        }

        if(
            (!trip.patient || !trip.patient.firstName || !trip.patient.lastName || !trip.patient.birthdate || (!trip.tripSeries && !trip.pickUpTime)) ||
            ((trip.tripSeries) && (!trip.startDate ||
                (!trip.monday && !trip.tuesday && !trip.wednesday && !trip.thursday && !trip.friday && !trip.saturday && !trip.sunday) || (trip.monday && !trip.mondayTime) || (trip.tuesday && !trip.tuesdayTime) || (trip.wednesday && !trip.wednesdayTime) ||
                (trip.thursday && !trip.thursdayTime) || (trip.friday && !trip.fridayTime) || (trip.saturday && !trip.saturdayTime) || (trip.sunday && !trip.sundayTime))) ||
            (!trip.locationNameFrom || (trip.locationNameFrom === "Krankenhaus" && !trip.stationFrom) || !trip.streetNameFrom || !trip.streetNrFrom || !trip.postCodeFrom || !trip.cityFrom ||
                !trip.countryFrom || !trip.locationNameTo || (trip.locationNameTo === "Krankenhaus" && !trip.stationTo) || !trip.streetNameTo || !trip.streetNrTo || !trip.postCodeTo ||
                !trip.cityTo || !trip.countryTo || (trip.costUnit === 2 && !trip.healthInsurance) || !trip.transportKind || !trip.tripType) ||
            (trip.returnTrip &&
                (trip.tripSeries && (((!trip.returnTripMonday && !trip.returnTripTuesday && !trip.returnTripWednesday &&! trip.returnTripThursday && !trip.returnTripFriday && !trip.returnTripSaturday && !trip.returnTripSunday) ||
                    (trip.returnTripMonday && !trip.returnTripMondayTime) || (trip.returnTripTuesday && !trip.returnTripTuesdayTime) || (trip.returnTripWednesday && !trip.returnTripWednesdayTime) ||
                    (trip.returnTripThursday && !trip.returnTripThursdayTime) || (trip.returnTripFriday && !trip.returnTripFridayTime) || (trip.returnTripSaturday && !trip.returnTripSaturdayTime) ||
                    (trip.returnTripSunday && !trip.returnTripSundayTime)) ||
                (!trip.returnTripLocationNameFrom || (trip.returnTripLocationNameFrom === "Krankenhaus" && !trip.returnTripStationFrom) || !trip.returnTripStreetNameFrom || !trip.returnTripStreetNrFrom ||
                    !trip.returnTripPostCodeFrom || !trip.returnTripCityFrom || !trip.returnTripCountryFrom || !trip.returnTripLocationNameTo || (trip.returnTripLocationNameTo === "Krankenhaus" && !trip.returnTripStationTo) ||
                    !trip.returnTripStreetNameTo || !trip.returnTripStreetNrTo || !trip.returnTripPostCodeTo || !trip.returnTripCityTo || !trip.returnTripCountryTo || (trip.returnTripCostUnit === 2 && !trip.returnTripHealthInsurance) ||
                    !trip.returnTripTransportKind || !trip.returnTripTripType))))) {
            props.showMessage(2,"Fehlende oder falsche Werte")
            setValidate(true)
        }else{
            showSaveTripRequestDialog()
        }
    }

    const saveChangeRequestOnClickHandler = (trip) => {
        if(trip.changeRequestPickUpTime || trip.returnTripChangeRequestPickUpTime || trip.changeRequestEndDate) {
            props.saveChangeRequest(trip);
            closeChangeRequestDialog();
        }else{
            props.showMessage(2,"Fehlende oder falsche Werte");
        }
    }

    const generateTripRequestPDF = (tripRequest) => {
        let pdf = new jsPDF('p','px');
        //format: ~445 x 630
        pdf.addFont('OpenSans-Regular.ttf', 'OpenSans-Regular', 'normal');
        pdf.setFont('OpenSans-Regular');
        if(props.company.logo) {
            pdf.addImage(props.company.logo, 185, 20, props.company.logoWidth, props.company.logoHeight);
        }
        pdf.setFontSize(16);
        pdf.text('Fahrtanfrage',40,120);
        pdf.setFontSize(12);
        pdf.text("von " + tripRequest.hospital.name,40,135);
        pdf.setFontSize(10);
        pdf.text("Patient",40,155);
        pdf.text("Vorname: " + tripRequest.patient.firstName,40,165);
        pdf.text("Nachname: " + tripRequest.patient.lastName,40,175);
        pdf.text("Geburtsdatum: " + HelperFunctions.formatDate(tripRequest.patient.birthdate),40,185);
        pdf.text("Station: " + (tripRequest.stationFrom?.name ? tripRequest.stationFrom.name : "-"),40,195);

        pdf.setFontSize(12);
        pdf.text(tripRequest.returnTrip ? "Hinfahrt" : "Einzelfahrt",40,215);

        pdf.setFontSize(10);
        pdf.text("Abholung",40,235);
        pdf.text("Zeitpunkt: " + (tripRequest.tripSeries ? "- (Serienfahrt)" : HelperFunctions.formatDateTime(tripRequest.pickUpTime) + (tripRequest.pickUpTimeRange ? " - " + HelperFunctions.formatTime(tripRequest.pickUpTimeRange) : "")),40,245);
        pdf.text("Ortsbezeichnung: " + tripRequest.locationNameFrom,40,255);
        pdf.text("Straßenname: " + tripRequest.streetNameFrom,40,265);
        pdf.text("Hausnummer: " + tripRequest.streetNrFrom,40,275);
        pdf.text("Adresszusatz: " + (tripRequest.addressAdditionFrom ? tripRequest.addressAdditionFrom : "-"),40,285);
        pdf.text("Postleitzahl: " + tripRequest.postCodeFrom,40,295);
        pdf.text("Stadt: " + tripRequest.cityFrom,40,305);
        pdf.text("Land: " + tripRequest.countryFrom,40,315);

        pdf.text("Ankunft",40,335);
        pdf.text("Ortsbezeichnung: " + tripRequest.locationNameTo,40,345);
        pdf.text("Straßenname: " + tripRequest.streetNameTo,40,355);
        pdf.text("Hausnummer: " + tripRequest.streetNrTo,40,365);
        pdf.text("Adresszusatz: " + (tripRequest.addressAdditionTo ? tripRequest.addressAdditionTo : "-"),40,375);
        pdf.text("Postleitzahl: " + tripRequest.postCodeTo,40,385);
        pdf.text("Stadt: " + tripRequest.cityTo,40,395);
        pdf.text("Land: " + tripRequest.countryTo,40,405);

        pdf.text("Sonstiges",40,425);
        pdf.text("Begleitperson: " + (tripRequest.companion ? "Ja" : "Nein"),40,435);
        pdf.text("Infektion: " + (tripRequest.infection ? "Ja" : "Nein"),40,445);
        let costUnit = "-";
        switch (tripRequest.costUnit) {
            case 1:
                costUnit = "Patient"
                break;
            case 2:
                costUnit = tripRequest.healthInsurance;
                break;
        }
        pdf.text("Kostenträger: " + costUnit,40,455);
        let transportKind = "-";
        switch (tripRequest.transportKind) {
            case 1:
                transportKind = "Tragestuhl"
                break;
            case 2:
                transportKind = "liegend";
                break;
            case 3:
                transportKind = "gehfähig";
                break;
        }
        pdf.text("Transportart: " + transportKind,40,465);
        let tripType = "-";
        switch (tripRequest.tripType) {
            case 1:
                tripType = "Einweisung"
                break;
            case 2:
                tripType = "Entlassung";
                break;
            case 3:
                tripType = "Sonstiges";
                break;
        }
        pdf.text("Fahrttyp: " + tripType,40,475);
        pdf.text("Kommentar: " + (tripRequest.comment ? tripRequest.comment : "-"),40,485);

        if(tripRequest.tripSeries) {
            pdf.text("Wiederholung",40,505);
            pdf.text("Montags: " + (tripRequest.monday ? tripRequest.mondayTime + " Uhr" : "-"),40,515);
            pdf.text("Dienstags: " + (tripRequest.tuesday ? tripRequest.tuesdayTime + " Uhr" : "-"),40,525);
            pdf.text("Mittwochs: " + (tripRequest.wednesday ? tripRequest.wednesdayTime + " Uhr" : "-"),40,535);
            pdf.text("Donnerstags: " + (tripRequest.thursday ? tripRequest.thursdayTime + " Uhr" : "-"),40,545);
            pdf.text("Freitags: " + (tripRequest.friday ? tripRequest.fridayTime + " Uhr" : "-"),40,555);
            pdf.text("Samstags: " + (tripRequest.saturday ? tripRequest.saturdayTime + " Uhr" : "-"),40,565);
            pdf.text("Sonntags: " + (tripRequest.sunday ? tripRequest.sundayTime + " Uhr" : "-"),40,575);
        }

        if(tripRequest.returnTrip) {
            pdf.setFontSize(12);
            pdf.text("Rückfahrt",240,215);

            pdf.setFontSize(10);
            pdf.text("Abholung",240,235);
            pdf.text("Zeitpunkt: " + (tripRequest.tripSeries ? "- (Serienfahrt)" : HelperFunctions.formatDateTime(tripRequest.returnTripPickUpTime) + (tripRequest.returnTripPickUpTimeRange ? " - " + HelperFunctions.formatTime(tripRequest.returnTripPickUpTimeRange) : "")),240,245);
            pdf.text("Ortsbezeichnung: " + tripRequest.returnTripLocationNameFrom,240,255);
            pdf.text("Straßenname: " + tripRequest.returnTripStreetNameFrom,240,265);
            pdf.text("Hausnummer: " + tripRequest.returnTripStreetNrFrom,240,275);
            pdf.text("Adresszusatz: " + (tripRequest.returnTripAddressAdditionFrom ? tripRequest.returnTripAddressAdditionFrom : "-"),240,285);
            pdf.text("Postleitzahl: " + tripRequest.returnTripPostCodeFrom,240,295);
            pdf.text("Stadt: " + tripRequest.returnTripCityFrom,240,305);
            pdf.text("Land: " + tripRequest.returnTripCountryFrom,240,315);

            pdf.text("Ankunft",240,335);
            pdf.text("Ortsbezeichnung: " + tripRequest.returnTripLocationNameTo,240,345);
            pdf.text("Straßenname: " + tripRequest.returnTripStreetNameTo,240,355);
            pdf.text("Hausnummer: " + tripRequest.returnTripStreetNrTo,240,365);
            pdf.text("Adresszusatz: " + (tripRequest.returnTripAddressAdditionTo ? tripRequest.returnTripAddressAdditionTo : "-"),240,375);
            pdf.text("Postleitzahl: " + tripRequest.returnTripPostCodeTo,240,385);
            pdf.text("Stadt: " + tripRequest.returnTripCityTo,240,395);
            pdf.text("Land: " + tripRequest.returnTripCountryTo,240,405);

            pdf.text("Sonstiges",240,425);
            pdf.text("Begleitperson: " + (tripRequest.returnTripCompanion ? "Ja" : "Nein"),240,435);
            pdf.text("Infektion: " + (tripRequest.returnTripInfection ? "Ja" : "Nein"),240,445);
            let returnTripCostUnit = "-";
            switch (tripRequest.returnTripCostUnit) {
                case 1:
                    returnTripCostUnit = "Patient"
                    break;
                case 2:
                    returnTripCostUnit = tripRequest.returnTripHealthInsurance;
                    break;
            }
            pdf.text("Kostenträger: " + costUnit,240,455);
            let returnTripTransportKind = "-";
            switch (tripRequest.returnTripTransportKind) {
                case 1:
                    returnTripTransportKind = "Tragestuhl"
                    break;
                case 2:
                    returnTripTransportKind = "liegend";
                    break;
                case 3:
                    returnTripTransportKind = "gehfähig";
                    break;
            }
            pdf.text("Transportart: " + returnTripTransportKind,240,465);
            let returnTripTripType = "-";
            switch (tripRequest.returnTripTripType) {
                case 1:
                    returnTripTripType = "Einweisung"
                    break;
                case 2:
                    returnTripTripType = "Entlassung";
                    break;
                case 3:
                    returnTripTripType = "Sonstiges";
                    break;
            }
            pdf.text("Fahrttyp: " + returnTripTripType,240,475);
            pdf.text("Kommentar: " + (tripRequest.returnTripComment ? tripRequest.returnTripComment : "-"),240,485);

            if(tripRequest.tripSeries) {
                pdf.text("Wiederholung",240,505);
                pdf.text("Startdatum: " + (tripRequest.startDate ? tripRequest.startDate : "-"),240,515);
                pdf.text("Enddatum: " + (tripRequest.startDate ? tripRequest.endDate : "-"),240,525);
                pdf.text("Montags: " + (tripRequest.returnTripMonday ? tripRequest.returnTripMondayTime + " Uhr" : "-"),240,535);
                pdf.text("Dienstags: " + (tripRequest.returnTripTuesday ? tripRequest.returnTripTuesdayTime + " Uhr" : "-"),240,545);
                pdf.text("Mittwochs: " + (tripRequest.returnTripWednesday ? tripRequest.returnTripWednesdayTime + " Uhr" : "-"),240,555);
                pdf.text("Donnerstags: " + (tripRequest.returnTripThursday ? tripRequest.returnTripThursdayTime + " Uhr" : "-"),240,565);
                pdf.text("Freitags: " + (tripRequest.returnTripFriday ? tripRequest.returnTripFridayTime + " Uhr" : "-"),240,575);
                pdf.text("Samstags: " + (tripRequest.returnTripSaturday ? tripRequest.returnTripSaturdayTime + " Uhr" : "-"),240,585);
                pdf.text("Sonntags: " + (tripRequest.returnTripSunday ? tripRequest.returnTripSundayTime + " Uhr" : "-"),240,595);
            }
        }

        pdf.save('fahrtanfrage_' + tripRequest.patient.firstName + tripRequest.patient.lastName + '.pdf');
    }

    // Dialog actions

    const showSaveTripRequestDialog = () => {
        setSaveTripRequestDialog(true)
    }

    const closeSaveTripRequestDialog = () => {
        setSaveTripRequestDialog(false)
    }

    const showConfirmTripRequestDialog = () => {
        setConfirmTripRequestDialog(true)
    }

    const closeConfirmTripRequestDialog = () => {
        setConfirmTripRequestDialog(false)
    }

    const showRejectTripRequestDialog = () => {
        setRejectTripRequestDialog(true)
    }

    const closeRejectTripRequestDialog = () => {
        setRejectTripRequestDialog(false)
    }

    const showCompleteTripDialog = () => {
        setCompleteTripDialog(true)
    }

    const closeCompleteTripDialog = () => {
        setCompleteTripDialog(false)
    }

    const showCompleteFirstTripOnlyDialog = () => {
        setCompleteFirstTripOnlyDialog(true)
    }

    const closeCompleteFirstTripOnlyDialog = () => {
        setCompleteFirstTripOnlyDialog(false)
    }

    const showChangeRequestDialog = () => {
        setChangeRequestDialog(true)
    }

    const closeChangeRequestDialog = () => {
        setChangeRequestDialog(false)
    }

    const showConfirmChangedTripRequestDialog = () => {
        setConfirmChangedTripRequestDialog(true)
    }

    const closeConfirmChangedTripRequestDialog = () => {
        setConfirmChangedTripRequestDialog(false)
    }

    const showRejectChangedTripRequestDialog = () => {
        setRejectChangedTripRequestDialog(true)
    }

    const closeRejectChangedTripRequestDialog = () => {
        setRejectChangedTripRequestDialog(false)
    }

    let defaultInputProps = {
        maxLength:45
    }

    let multilineInputProps = {
        maxLength:255
    }

    const filter = createFilterOptions({limit:30});

    return (
        <Dialog scroll='body' fullWidth open={props.open} onClose={props.close} className='trip-dialog'>

            <Tooltip id="trip-dialog-tooltip"/>
            <YesCancelDialog
                open={saveTripRequestDialog}
                close={closeSaveTripRequestDialog}
                header='Anfragen'
                text='Wollen Sie die Fahrt wirklich anfragen? Änderungen sind dann nicht mehr möglich.'
                onClick={() => props.createTripRequest(trip)}
            />

            <YesCancelDialog
                open={rejectTripRequestDialog}
                close={closeRejectTripRequestDialog}
                header='Ablehnen'
                text='Wollen Sie die Fahrtanfrage wirklich ablehnen? Sie können dies nicht rückgängig machen.'
                onClick={() => props.rejectTripRequest(trip)}
            />

            <YesCancelDialog
                open={confirmTripRequestDialog}
                close={closeConfirmTripRequestDialog}
                header='Bestätigen'
                text='Wollen Sie die Fahrtanfrage wirklich bestätigen? Sie können dies nicht rückgängig machen.'
                onClick={() => props.confirmTripRequest(trip)}
            />

            <YesCancelDialog
                open={completeTripDialog}
                close={closeCompleteTripDialog}
                header='Abschließen'
                text='Wollen Sie die Fahrtanfrage wirklich abschließen? Sie können dies nicht rückgängig machen.'
                onClick={() => props.completeTrip(trip)}
            />

            <YesCancelDialog
                open={completeFirstTripOnlyDialog}
                close={closeCompleteFirstTripOnlyDialog}
                header='Hinfahrt Abschließen'
                text='Wollen Sie die Hinfahrt wirklich abschließen? Sie können dies nicht rückgängig machen.'
                onClick={() => props.completeFirstTripOnly(trip)}
            />

            <YesCancelDialog
                open={confirmChangedTripRequestDialog}
                close={closeConfirmChangedTripRequestDialog}
                header='Änderungsanfrage bestätigen'
                text='Wollen Sie die Änderungsanfrage wirklich bestätigen? Sie können dies nicht rückgängig machen.'
                onClick={() => props.confirmChangedTrip(trip)}
            />

            <YesCancelDialog
                open={rejectChangedTripRequestDialog}
                close={closeRejectChangedTripRequestDialog}
                header='Änderungsanfrage ablehnen'
                text='Wollen Sie die Änderungsanfrage wirklich ablehnen? Die Fahrtanfrage wird dadurch gelöscht und Sie können dies nicht rückgängig machen.'
                onClick={() => props.rejectChangedTrip(trip)}
            />

            {props.type === "trip-request" || props.type === "trip-series" ? <ChangeRequestDialog
                open={changeRequestDialog}
                close={closeChangeRequestDialog}
                showMessage={props.showMessage}
                trip={trip}
                formatDate={props.formatDate}
                saveChangeRequestOnClickHandler={saveChangeRequestOnClickHandler}
                changeRequestPickUpTimeOnChangeHandler={changeRequestPickUpTimeOnChangeHandler}
                changeRequestPickUpTimeRangeOnChangeHandler={changeRequestPickUpTimeRangeOnChangeHandler}
                returnTripChangeRequestPickUpTimeOnChangeHandler={returnTripChangeRequestPickUpTimeOnChangeHandler}
                returnTripChangeRequestPickUpTimeRangeOnChangeHandler={returnTripChangeRequestPickUpTimeRangeOnChangeHandler}
                changeRequestEndDateOnChangeHandler={changeRequestEndDateOnChangeHandler}
            /> : null}

            <CloseIcon onClick={props.close} className='icon close-icon'/>
            <DialogTitle><h1>
                {props.type === "trip-request" ? "Fahrtanfrage" : null}
                {props.type === "trip" ? "Fahrt" : null}
                {props.type === "trip-series" ? "Serienfahrt" : null}
                {props.type === "completed-trip" ? "Abgeschlossene Fahrt" : null}
            </h1></DialogTitle>
            <DialogContent>
                {props.type === "trip-request" && (props.user && (props.user.role === "ADMIN" ||props.user.role === "USER")) && props.trip && props.trip.id ? <div>
                    <h2>Aktionen</h2>
                    <PictureAsPdfIcon data-tip="Fahrtanfrage als PDF generieren" className="icon" onClick={() => generateTripRequestPDF(trip)}/>
                </div> : null}
                <div className="patient-div">
                    <Autocomplete
                        disabled={trip && trip.id}
                        value={trip && trip.patient && trip.patient.firstName ? trip.patient.firstName : ''}
                        onChange={(event, newValue) => {
                            if (typeof newValue === 'string') {
                                patientOnChangeHandler({
                                    firstName: newValue,
                                });
                            } else if (newValue && newValue.inputValue) {
                                // Create a new value from the user input
                                patientOnChangeHandler({
                                    firstName: newValue.inputValue,
                                });
                            } else {
                                patientOnChangeHandler(newValue);
                            }
                        }}
                        filterOptions={(options, params) => {
                            const filtered = filter(options, params);
                            const {inputValue} = params;
                            if (inputValue.length === 0) {
                                return [];
                            }

                            return filtered.slice(0, 20);
                        }}
                        selectOnFocus
                        clearOnBlur
                        handleHomeEndKeys
                        className='field'
                        options={props.patients}
                        getOptionLabel={(option) => {
                            // Value selected with enter, right from the input
                            if (typeof option === 'string') {
                                return option;
                            }
                            // Add "xxx" option created dynamically
                            if (option.inputValue) {
                                return option.inputValue;
                            }
                            // Regular option
                            return option.firstName + option.lastName + HelperFunctions.formatDate(option.birthdate);
                        }}

                        renderOption={(props, option) =>
                            <li {...props}>{(option.firstName ? option.firstName : "") + (option.lastName ? " " + option.lastName : "") + (option.birthdate ? " (" + HelperFunctions.formatDate(option.birthdate) + ")" : "")}</li>}
                        freeSolo
                        renderInput={(params) => (
                            <ToolTextField {...params}
                                           inputProps={{...params.inputProps, maxLength: 45}}
                                           InputLabelProps={{shrink: true}}
                                           onChange={patientFirstNameOnChangeHandler}
                                           error={validate && (!trip || !trip.patient || !trip.patient.firstName)}
                                           label="Vorname"/>
                        )}
                    />
                    <Autocomplete
                        disabled={trip && trip.id}
                        value={trip && trip.patient && trip.patient.lastName ? trip.patient.lastName : ''}
                        onChange={(event, newValue) => {
                            if (typeof newValue === 'string') {
                                patientOnChangeHandler({
                                    lastName: newValue,
                                });
                            } else if (newValue && newValue.inputValue) {
                                // Create a new value from the user input
                                patientOnChangeHandler({
                                    lastName: newValue.inputValue,
                                });
                            } else {
                                patientOnChangeHandler(newValue);
                            }
                        }}
                        filterOptions={(options, params) => {
                            const filtered = filter(options, params);
                            const {inputValue} = params;
                            if (inputValue.length === 0) {
                                return [];
                            }

                            return filtered.slice(0, 20);
                        }}
                        selectOnFocus
                        clearOnBlur
                        handleHomeEndKeys
                        className='field'
                        options={props.patients}
                        getOptionLabel={(option) => {
                            // Value selected with enter, right from the input
                            if (typeof option === 'string') {
                                return option;
                            }
                            // Add "xxx" option created dynamically
                            if (option.inputValue) {
                                return option.inputValue;
                            }
                            // Regular option
                            return option.firstName + option.lastName + HelperFunctions.formatDate(option.birthdate);
                        }}

                        renderOption={(props, option) =>
                            <li {...props}>{(option.firstName ? option.firstName : "") + (option.lastName ? " " + option.lastName : "") + (option.birthdate ? " (" + HelperFunctions.formatDate(option.birthdate) + ")" : "")}</li>}
                        freeSolo
                        renderInput={(params) => (
                            <ToolTextField {...params}
                                           inputProps={{...params.inputProps, maxLength: 45}}
                                           InputLabelProps={{shrink: true}}
                                           onChange={patientLastNameOnChangeHandler}
                                           error={validate && (!trip || !trip.patient || !trip.patient.lastName)}
                                           label="Nachname"/>
                        )}
                    />
                    <Autocomplete
                        disabled={trip && trip.id}
                        value={trip && trip.patient && trip.patient.birthdate ? trip.patient.birthdate : ''}
                        onChange={(event, newValue) => {
                            if (typeof newValue === 'string') {
                                patientOnChangeHandler({
                                    birthdate: newValue,
                                });
                            } else if (newValue && newValue.inputValue) {
                                // Create a new value from the user input
                                patientOnChangeHandler({
                                    birthdate: newValue.inputValue,
                                });
                            } else {
                                patientOnChangeHandler(newValue);
                            }
                        }}
                        filterOptions={(options, params) => {
                            const filtered = filter(options, params);
                            const {inputValue} = params;
                            if (inputValue.length === 0) {
                                return [];
                            }

                            return filtered.slice(0, 20);
                        }}
                        selectOnFocus
                        clearOnBlur
                        handleHomeEndKeys
                        className='field'
                        options={props.patients}
                        getOptionLabel={(option) => {
                            // Value selected with enter, right from the input
                            if (typeof option === 'string') {
                                return option;
                            }
                            // Add "xxx" option created dynamically
                            if (option.inputValue) {
                                return option.inputValue;
                            }
                            // Regular option
                            return option.firstName + option.lastName + HelperFunctions.formatDate(option.birthdate);
                        }}

                        renderOption={(props, option) => <li {...props}>{(option.firstName ? option.firstName : "") + (option.lastName ? " " + option.lastName : "") + (option.birthdate ? " (" + HelperFunctions.formatDate(option.birthdate) + ")" : "")}</li>}
                        freeSolo
                        renderInput={(params) => (
                            <ToolTextField {...params}
                                           inputProps={{...params.inputProps, maxLength: 45}}
                                           InputLabelProps={{shrink: true}}
                                           onChange={patientBirthdateOnChangeHandler}
                                           type="date"
                                           error={validate && (!trip || !trip.patient || !trip.patient.birthdate)}
                                           label="Geburtsdatum"/>
                        )}
                    />
                    <ToolTextField fullWidth InputLabelProps={{shrink: true}} inputProps={defaultInputProps} disabled={props.user.stationUser || trip && trip.id} className='field' label="Station (optional)"
                                   value={trip?.patient?.station?.id ? trip.patient.station.id : ""}
                                   onChange={patientStationOnChangeHandler} select>
                        {props.stations ? props.stations.map(station => (
                            <MenuItem key={station.id} value={station.id}>{station.name}</MenuItem>
                        )) : null}
                    </ToolTextField>
                </div>
                <div>
                    <h2>Abholung</h2>
                    <FormControlLabel disabled={trip && trip.id} className='field'
                                      control={<Checkbox onChange={toggleTripSeries}
                                                         checked={trip && trip.tripSeries}/>} label="Serienfahrt"/>
                    {trip && trip.tripSeries ? <div>
                        <div>
                            <ToolTextField error={validate && (!trip || (trip.tripSeries && !trip.startDate))} fullWidth inputProps={defaultInputProps}
                                       disabled={trip && trip.id} type="date" className='field' helperText="Startdatum"
                                       value={trip && trip.startDate ? trip.startDate : ""}
                                       onChange={startDateOnChangeHandler}/>
                        </div>
                        <div>
                            <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} type="date"
                                       className='field' helperText="Enddatum (optional)"
                                       value={trip && trip.endDate ? trip.endDate : ""}
                                       onChange={endDateOnChangeHandler}/>
                            {trip && trip.changeRequest && trip.changeRequestEndDate ?
                                <ToolTextField fullWidth inputProps={defaultInputProps} disabled type="date"
                                           className='field'
                                           helperText={<p className="changed-info-text">Neues Enddatum (optional)</p>}
                                           value={trip && trip.changeRequestEndDate ? trip.changeRequestEndDate : ""}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id}
                                              control={<Checkbox onChange={toggleMonday}
                                                                 checked={trip && trip.monday}/>} label="Montag"/>
                            {trip && trip.monday ?
                                <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.monday && !trip.mondayTime))} inputProps={defaultInputProps}
                                           disabled={trip && trip.id} className='field' type='time' label="Zeit"
                                           value={trip && trip.mondayTime ? trip.mondayTime : ""}
                                           onChange={mondayTimeOnChangeHandler}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id}
                                              control={<Checkbox onChange={toggleTuesday}
                                                                 checked={trip && trip.tuesday}/>} label="Dienstag"/>
                            {trip && trip.tuesday ?
                                <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.tuesday && !trip.tuesdayTime))} inputProps={defaultInputProps}
                                           disabled={trip && trip.id} className='field' type='time' label="Zeit"
                                           value={trip && trip.tuesdayTime ? trip.tuesdayTime : ""}
                                           onChange={tuesdayTimeOnChangeHandler}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id}
                                              control={<Checkbox onChange={toggleWednesday}
                                                                 checked={trip && trip.wednesday}/>} label="Mittwoch"/>
                            {trip && trip.wednesday ?
                                <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.wednesday && !trip.wednesdayTime))} inputProps={defaultInputProps}
                                           disabled={trip && trip.id} className='field' type='time' label="Zeit"
                                           value={trip && trip.wednesdayTime ? trip.wednesdayTime : ""}
                                           onChange={wednesdayTimeOnChangeHandler}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id}
                                              control={<Checkbox onChange={toggleThursday}
                                                                 checked={trip && trip.thursday}/>} label="Donnerstag"/>
                            {trip && trip.thursday ?
                                <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.thursday && !trip.thursdayTime))} inputProps={defaultInputProps}
                                           disabled={trip && trip.id} className='field' type='time' label="Zeit"
                                           value={trip && trip.thursdayTime ? trip.thursdayTime : ""}
                                           onChange={thursdayTimeOnChangeHandler}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id}
                                              control={<Checkbox onChange={toggleFriday}
                                                                 checked={trip && trip.friday}/>} label="Freitag"/>
                            {trip && trip.friday ?
                                <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.friday && !trip.fridayTime))} inputProps={defaultInputProps}
                                           disabled={trip && trip.id} className='field' type='time' label="Zeit"
                                           value={trip && trip.fridayTime ? trip.fridayTime : ""}
                                           onChange={fridayTimeOnChangeHandler}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id}
                                              control={<Checkbox onChange={toggleSaturday}
                                                                 checked={trip && trip.saturday}/>} label="Samstag"/>
                            {trip && trip.saturday ?
                                <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.saturday && !trip.saturdayTime))} inputProps={defaultInputProps}
                                           disabled={trip && trip.id} className='field' type='time' label="Zeit"
                                           value={trip && trip.saturdayTime ? trip.saturdayTime : ""}
                                           onChange={saturdayTimeOnChangeHandler}/> : null}
                        </div>
                        <div>
                            <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.monday || !trip.tuesday || !trip.wednesday || !trip.thursday || !trip.friday || !trip.saturday || !trip.sunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleSunday} checked={trip && trip.sunday}/>} label="Sonntag" />
                            {trip && trip.sunday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.sunday && !trip.sundayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.sundayTime ? trip.sundayTime : ""} onChange={sundayTimeOnChangeHandler}/> : null}
                        </div>
                    </div> : <div>
                        <ToolTextField fullWidth error={validate && (!trip || !trip.pickUpTime)} inputProps={defaultInputProps} disabled={trip && trip.id} type='datetime-local' className='field' InputLabelProps={{shrink: true}} label="Zeitpunkt" value={trip && trip.pickUpTime ? trip.pickUpTime : ""} onChange={pickUpTimeOnChangeHandler}/>
                        <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} type='datetime-local' className='field' InputLabelProps={{shrink: true}} label="spätester Zeitpunkt (optional)" value={trip && trip.pickUpTimeRange ? trip.pickUpTimeRange : ""} onChange={pickUpTimeRangeOnChangeHandler}/>
                        {trip && trip.changeRequest ?
                            <div>
                                {trip.changeRequestPickUpTime ? <ToolTextField fullWidth inputProps={defaultInputProps} disabled type="datetime-local" className='field' helperText={<p className="changed-info-text">Neuer Zeitpunkt</p>} value={trip && trip.changeRequestPickUpTime ? trip.changeRequestPickUpTime : ""}/> : null}
                                {trip.changeRequestPickUpTimeRange ? <ToolTextField fullWidth inputProps={defaultInputProps} disabled type="datetime-local" className='field' helperText={<p className="changed-info-text">Neuer spätester Zeitpunkt (optional)</p>} value={trip && trip.changeRequestPickUpTimeRange ? trip.changeRequestPickUpTimeRange : ""}/> : null}
                            </div> : null}
                    </div>}
                    <Autocomplete

                        disabled={trip && trip.id}
                        inputProps={defaultInputProps}
                        freeSolo
                        options={["Krankenhaus","Praxis","Wohnung"]}
                        className="field"
                        value={trip && trip.locationNameFrom ? trip.locationNameFrom : ""}
                        onChange={(event, newValue) => {
                            if (typeof newValue === 'string') {
                                locationNameFromOnChangeHandler(newValue);
                            } else if (newValue && newValue.inputValue) {
                                // Create a new value from the user input
                                locationNameFromOnChangeHandler(newValue.inputValue);
                                //setState({isNewCustomer:true});
                            } else {
                                locationNameFromOnChangeHandler(newValue);
                                //setState({isNewCustomer:false});
                            }
                        }}
                        renderInput={(params) => <ToolTextField error={validate && (!trip || !trip.locationNameFrom)} fullWidth onChange={(e) => locationNameFromOnChangeHandler(e.target.value)} {...params} label="Ortsbezeichnung" />}
                    />
                    {trip && trip.locationNameFrom === "Krankenhaus" ? trip.id ?
                        <p className="field station-info">Station: {trip.stationFrom?.name}</p>
                        : <ToolTextField error={validate && (!trip || (trip.locationNameFrom === "Krankenhaus" && !trip.stationFrom))} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} value={trip.stationFrom?.id ? trip.stationFrom.id : ""} onChange={stationFromOnChangeHandler} select className='field' label='Station'>
                            {props.stations ? props.stations.map(station => (
                                <MenuItem key={station.id} value={station.id}>
                                    {station.name}
                                </MenuItem>
                            )) : null}
                        </ToolTextField> : null}
                    <ToolTextField fullWidth error={validate && (!trip || !trip.streetNameFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Straße" value={trip && trip.streetNameFrom ? trip.streetNameFrom : ""} onChange={streetNameFromOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.streetNrFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Hausnummer" value={trip && trip.streetNrFrom ? trip.streetNrFrom : ""} onChange={streetNrFromOnChangeHandler}/>
                    <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Adresszusatz (optional)" value={trip && trip.addressAdditionFrom ? trip.addressAdditionFrom : ""} onChange={addressAdditionFromOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.postCodeFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Postleitzahl" value={trip && trip.postCodeFrom ? trip.postCodeFrom : ""} onChange={postCodeFromOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.cityFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Stadt" value={trip && trip.cityFrom ? trip.cityFrom : ""} onChange={cityFromOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.countryFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label='Land' value={trip && trip.countryFrom ? trip.countryFrom : ""} select onChange={countryFromOnChangeHandler}>
                        {props.countries}
                    </ToolTextField>
                </div>
                <div>
                    <h2>Ankunft</h2>
                    {trip && !trip.tripSeries ? <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='datetime-local' InputLabelProps={{shrink: true}} label="Zeit (optional)" value={trip && trip.arrivalTime ? trip.arrivalTime : ""} onChange={arrivalTimeOnChangeHandler}/> : null}
                    <Autocomplete

                        disabled={trip && trip.id}
                        inputProps={defaultInputProps}
                        freeSolo
                        options={["Krankenhaus","Praxis","Wohnung"]}
                        className="field"
                        value={trip && trip.locationNameTo ? trip.locationNameTo : ""}
                        onChange={(event, newValue) => {
                            if (typeof newValue === 'string') {
                                locationNameToOnChangeHandler(newValue);
                            } else if (newValue && newValue.inputValue) {
                                // Create a new value from the user input
                                locationNameToOnChangeHandler(newValue.inputValue);
                                //setState({isNewCustomer:true});
                            } else {
                                locationNameToOnChangeHandler(newValue);
                                //setState({isNewCustomer:false});
                            }
                        }}
                        renderInput={(params) => <ToolTextField error={validate && (!trip || !trip.locationNameTo)} fullWidth onChange={(e) => locationNameToOnChangeHandler(e.target.value)} {...params} label="Ortsbezeichnung" />}
                    />

                    {trip && trip.locationNameTo === "Krankenhaus" ? trip.id ?
                        <p className="field station-info">Station: {trip.stationTo?.name}</p> : <ToolTextField fullWidth error={validate && (!trip || (trip.locationNameTo === "Krankenhaus" && !trip.stationTo))} inputProps={defaultInputProps} disabled={trip && trip.id} value={trip?.stationTo?.id ? trip.stationTo.id : null} onChange={stationToOnChangeHandler} select className='field' label='Station'>
                            {props.stations ? props.stations.map(station => (
                                <MenuItem key={station.id} value={station.id}>
                                    {station.name}
                                </MenuItem>
                            )) : null}
                        </ToolTextField> : null}
                    <ToolTextField fullWidth error={validate && (!trip || !trip.streetNameTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Straße" value={trip && trip.streetNameTo ? trip.streetNameTo : ""} onChange={streetNameToOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.streetNrTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Hausnummer" value={trip && trip.streetNrTo ? trip.streetNrTo : ""} onChange={streetNrToOnChangeHandler}/>
                    <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Adresszusatz (optional)" value={trip && trip.addressAdditionTo ? trip.addressAdditionTo : ""} onChange={addressAdditionToOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.postCodeTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Postleitzahl" value={trip && trip.postCodeTo ? trip.postCodeTo : ""} onChange={postCodeToOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.cityTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Stadt" value={trip && trip.cityTo ? trip.cityTo : ""} onChange={cityToOnChangeHandler}/>
                    <ToolTextField fullWidth error={validate && (!trip || !trip.countryTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label='Land' value={trip && trip.countryTo ? trip.countryTo : ""} select onChange={countryToOnChangeHandler}>
                        {props.countries}
                    </ToolTextField>
                </div>
                <div>
                    <h2>Sonstiges</h2>
                    <FormControlLabel disabled={trip && trip.id} className='field' control={<Checkbox onChange={toggleCompanion} value={trip && trip.companion !== null}/>} label="Begleitperson" />
                    <FormControlLabel disabled={trip && trip.id} className='field' control={<Checkbox onChange={toggleInfection} value={trip && trip.infection}/>} label="Infektion" />
                    <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' value={trip && trip.costUnit ? trip.costUnit : ""} onChange={costUnitOnChangeHandler} select label='Kostenträger (optional)'>
                        <MenuItem key={1} value={1}>Patient</MenuItem>
                        <MenuItem key={2} value={2}>Krankenkasse</MenuItem>
                    </ToolTextField>
                    {trip && trip.costUnit && trip.costUnit === 2 ?
                        <Autocomplete
                            disabled={trip && trip.id}
                            inputProps={defaultInputProps}
                            freeSolo
                            options={[
                                "Techniker Krankenkasse",
                                "Barmer Ersatzkasse",
                                "DAK-Gesundheit",
                                "AOK",
                                "IKK classic",
                                "Kaufmännische Krankenkasse",
                                "Knappschaft",
                                "Siemens-Betriebskrankenkasse",
                                "Handelskrankenkasse",
                                "Audi BKK"
                            ]}
                            className="field"
                            value={trip?.healthInsurance || ""}
                            onChange={(event, newValue) => {
                                if (typeof newValue === "string") {
                                    setTrip({ ...trip, healthInsurance: newValue });
                                } else if (newValue && newValue.inputValue) {
                                    setTrip({ ...trip, healthInsurance: newValue.inputValue });
                                } else {
                                    setTrip({ ...trip, healthInsurance: newValue });
                                }
                            }}
                            renderInput={(params) => (
                                <ToolTextField
                                    {...params}
                                    fullWidth
                                    error={validate && (!trip || (trip.costUnit === 2 && !trip.healthInsurance))}
                                    onChange={(e) => setTrip({ ...trip, healthInsurance: e.target.value })}
                                    label="Krankenkasse"
                                />
                            )}
                        /> : null}
                    <ToolTextField error={validate && (!trip || !trip.transportKind)} fullWidth disabled={trip && trip.id} className='field' value={trip && trip.transportKind ? trip.transportKind : ""} onChange={transportKindOnChangeHandler} select label='Transportart'>
                        <MenuItem key={1} value={1}>Tragestuhl</MenuItem>
                        <MenuItem key={2} value={2}>liegend</MenuItem>
                        {props.station && props.station.walkable ? <MenuItem key={3} value={3}>gefähig</MenuItem> : null}
                    </ToolTextField>
                    <ToolTextField error={validate && (!trip || !trip.tripType)} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' value={trip && trip.tripType ? trip.tripType : ""} onChange={tripTypeOnChangeHandler} select label='Fahrttyp'>
                        <MenuItem key={1} value={1}>Einweisung</MenuItem>
                        <MenuItem key={2} value={2}>Entlassung</MenuItem>
                        <MenuItem key={3} value={3}>Sonstiges</MenuItem>
                    </ToolTextField>
                    <ToolTextField fullWidth disabled={trip && trip.id} multiline inputProps={multilineInputProps} minRows={7} className='multiline' label="Kommentar (optional)" value={trip && trip.comment ? trip.comment : ""} onChange={commentOnChangeHandler}/>
                    {props.type === "trip-request" ? <FormControlLabel disabled={trip && trip.id} className='field' control={<Checkbox checked={trip ? trip.returnTrip : false} onChange={toggleReturnTrip}/>} label="Inklusive Rückfahrt" /> : null}
                </div>

                {trip && trip.returnTrip ?
                    <div>
                        <h1 className='return-trip-title'>Rückfahrt</h1>
                        <div>
                            <h2>Abholung</h2>
                            {trip && trip.tripSeries ? <div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripMonday} checked={trip && trip.returnTripMonday}/>} label="Montag" />
                                    {trip && trip.returnTripMonday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.returnTripMonday && !trip.returnTripMondayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripMondayTime ? trip.returnTripMondayTime : ""} onChange={returnTripMondayTimeOnChangeHandler}/> : null}
                                </div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripTuesday} checked={trip && trip.returnTripTuesday}/>} label="Dienstag" />
                                    {trip && trip.returnTripTuesday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.returnTripTuesday && !trip.returnTripTuesdayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripTuesdayTime ? trip.returnTripTuesdayTime : ""} onChange={returnTripTuesdayTimeOnChangeHandler}/> : null}
                                </div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripWednesday} checked={trip && trip.returnTripWednesday}/>} label="Mittwoch" />
                                    {trip && trip.returnTripWednesday ? <ToolTextField error={validate && (!trip || (trip.tripSeries && trip.returnTripWednesday && !trip.returnTripWednesdayTime))} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripWednesdayTime ? trip.returnTripWednesdayTime : ""} onChange={returnTripWednesdayTimeOnChangeHandler}/> : null}
                                </div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripThursday} checked={trip && trip.returnTripThursday}/>} label="Donnerstag" />
                                    {trip && trip.returnTripThursday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.returnTripThursday && !trip.returnTripThursdayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripThursdayTime ? trip.returnTripThursdayTime : ""} onChange={returnTripThursdayTimeOnChangeHandler}/> : null}
                                </div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripFriday} checked={trip && trip.returnTripFriday}/>} label="Freitag" />
                                    {trip && trip.returnTripFriday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.returnTripFriday && !trip.returnTripFridayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripFridayTime ? trip.returnTripFridayTime : ""} onChange={returnTripFridayTimeOnChangeHandler}/> : null}
                                </div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripSaturday} checked={trip && trip.returnTripSaturday}/>} label="Samstag" />
                                    {trip && trip.returnTripSaturday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.returnTripSaturday && !trip.returnTripSaturdayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripSaturdayTime ? trip.returnTripSaturdayTime : ""} onChange={returnTripSaturdayTimeOnChangeHandler}/> : null}
                                </div>
                                <div>
                                    <FormControlLabel className='field' error={validate && (!trip || (trip.tripSeries && (!trip.returnTripMonday || !trip.returnTripTuesday || !trip.returnTripWednesday || !trip.returnTripThursday || !trip.returnTripFriday || !trip.returnTripSaturday || !trip.returnTripSunday)))} disabled={trip && trip.id} control={<Checkbox onChange={toggleReturnTripSunday} checked={trip && trip.returnTripSunday}/>} label="Sonntag" />
                                    {trip && trip.returnTripSunday ? <ToolTextField fullWidth error={validate && (!trip || (trip.tripSeries && trip.returnTripSunday && !trip.returnTripSundayTime))} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='time' label="Zeit" value={trip && trip.returnTripSundayTime ? trip.returnTripSundayTime : ""} onChange={returnTripSundayTimeOnChangeHandler}/> : null}
                                </div>
                            </div> : <div>
                                <ToolTextField error={validate && (!trip || !trip.returnTripPickUpTime)} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='datetime-local' helperText="Abholzeit" value={trip.returnTripPickUpTime} onChange={returnTripPickUpTimeOnChangeHandler}/>
                                <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='datetime-local' helperText="späteste Abholzeit (optional)" value={trip.returnTripPickUpTimeRange} onChange={returnTripPickUpTimeRangeOnChangeHandler}/>
                                {trip && trip.changeRequest ?
                                    <div>
                                        <ToolTextField fullWidth inputProps={defaultInputProps} disabled type="datetime-local" className='field' helperText={<p className="changed-info-text">Neue Abholzeit</p>} value={trip && trip.returnTripChangeRequestPickUpTime ? trip.returnTripChangeRequestPickUpTime : ""}/>
                                        <ToolTextField fullWidth inputProps={defaultInputProps} disabled type="datetime-local" className='field' helperText={<p className="changed-info-text">Neue späteste Abholzeit (optional)</p>} value={trip && trip.returnTripChangeRequestPickUpTimeRange ? trip.returnTripChangeRequestPickUpTimeRange : ""}/>
                                    </div> : null}
                            </div>}
                            <Autocomplete

                                disabled={trip && trip.id}
                                inputProps={defaultInputProps}
                                freeSolo
                                options={["Krankenhaus","Praxis","Wohnung"]}
                                className="field"
                                value={trip.returnTripLocationNameFrom}
                                onChange={(event, newValue) => {
                                    if (typeof newValue === 'string') {
                                        returnTripLocationNameFromOnChangeHandler(newValue);
                                    } else if (newValue && newValue.inputValue) {
                                        // Create a new value from the user input
                                        returnTripLocationNameFromOnChangeHandler(newValue.inputValue);
                                        //setState({isNewCustomer:true});
                                    } else {
                                        returnTripLocationNameFromOnChangeHandler(newValue);
                                        //setState({isNewCustomer:false});
                                    }
                                }}
                                renderInput={(params) => <ToolTextField error={validate && (!trip || !trip.returnTripLocationNameFrom)} fullWidth onChange={(e) => returnTripLocationNameFromOnChangeHandler(e.target.value)} {...params} label="Ortsbezeichnung" />}
                            />

                            {trip && trip.returnTripLocationNameFrom === "Krankenhaus" ? trip.id ?
                                <p className="field station-info">Station: {trip.returnTripStationFrom?.name}</p> : <ToolTextField error={validate && (!trip || (trip.returnTripLocationNameFrom === "Krankenhaus" && !trip.returnTripStationFrom))} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} value={trip?.returnTripStationFrom?.id ? trip.returnTripStationFrom.id : null} onChange={returnTripStationFromOnChangeHandler} select className='field' label='Station'>
                                    {props.stations ? props.stations.map(station => (
                                        <MenuItem key={station.id} value={station.id}>
                                            {station.name}
                                        </MenuItem>
                                    )) : null}
                                </ToolTextField> : null}
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripStreetNameFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Straße" value={trip.returnTripStreetNameFrom} onChange={returnTripStreetNameFromOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripStreetNrFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Hausnummer" value={trip.returnTripStreetNrFrom} onChange={returnTripStreetNrFromOnChangeHandler}/>
                            <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Adresszusatz (optional)" value={trip.returnTripAddressAdditionFrom} onChange={returnTripAddressAdditionFromOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripPostCodeFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Postleitzahl" value={trip.returnTripPostCodeFrom} onChange={returnTripPostCodeFromOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripCityFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Stadt" value={trip.returnTripCityFrom} onChange={returnTripCityFromOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripCountryFrom)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label='Land' value={trip.returnTripCountryFrom ? trip.returnCountryFrom : ""} select onChange={returnTripCountryFromOnChangeHandler}>
                                {props.countries}
                            </ToolTextField>
                        </div>
                        <div>
                            <h2>Ankunft</h2>
                            {trip && !trip.tripSeries ? <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' type='datetime-local' helperText="Zeit (optional)" value={trip.returnTripArrivalTime} onChange={returnTripArrivalTimeOnChangeHandler}/> : null}
                            <Autocomplete

                                disabled={trip && trip.id}
                                inputProps={defaultInputProps}
                                freeSolo
                                options={["Krankenhaus","Praxis","Wohnung"]}
                                className="field"
                                value={trip.returnTripLocationNameTo}
                                onChange={(event, newValue) => {
                                    if (typeof newValue === 'string') {
                                        returnTripLocationNameToOnChangeHandler(newValue);
                                    } else if (newValue && newValue.inputValue) {
                                        // Create a new value from the user input
                                        returnTripLocationNameToOnChangeHandler(newValue.inputValue);
                                        //setState({isNewCustomer:true});
                                    } else {
                                        returnTripLocationNameToOnChangeHandler(newValue);
                                        //setState({isNewCustomer:false});
                                    }
                                }}
                                renderInput={(params) => <ToolTextField error={validate && (!trip || !trip.returnTripLocationNameTo)} fullWidth onChange={(e) => returnTripLocationNameToOnChangeHandler(e.target.value)} {...params} label="Ortsbezeichnung" />}
                            />
                            {trip && trip.returnTripLocationNameTo === "Krankenhaus" ? trip.id ?
                                <p className="field station-info">Station: {trip.returnTripStationTo?.name}</p> :
                                <ToolTextField error={validate && (!trip || (trip.returnTripLocationNameTo === "Krankenhaus" && !trip.returnTripStationTo))} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} value={trip?.returnTripStationTo?.id ? trip.returnTripStationTo.id : null} onChange={returnTripStationToOnChangeHandler} select className='field' label='Station'>
                                    {props.stations ? props.stations.map(station => (
                                        <MenuItem key={station.id} value={station.id}>
                                            {station.name}
                                        </MenuItem>
                                    )) : null}
                                </ToolTextField> : null}
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripStreetNameTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Straße" value={trip.returnTripStreetNameTo} onChange={returnTripStreetNameToOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripStreetNrTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Hausnummer" value={trip.returnTripStreetNrTo} onChange={returnTripStreetNrToOnChangeHandler}/>
                            <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Adresszusatz (optional)" value={trip.returnTripAddressAdditionTo} onChange={returnTripAddressAdditionToOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripPostCodeTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Postleitzahl" value={trip.returnTripPostCodeTo} onChange={returnTripPostCodeToOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripCityTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label="Stadt" value={trip.returnTripCityTo} onChange={returnTripCityToOnChangeHandler}/>
                            <ToolTextField fullWidth error={validate && (!trip || !trip.returnTripCountryTo)} inputProps={defaultInputProps} disabled={trip && trip.id} className='field' label='Land' value={trip.returnTripCountryTo ? trip.returnTripCountryTo : ""} select onChange={returnTripCountryToOnChangeHandler}>
                                {props.countries}
                            </ToolTextField>
                        </div>
                        <div>
                            <h2>Sonstiges</h2>
                            <FormControlLabel disabled={trip && trip.id} className='field' control={<Checkbox onChange={returnTripToggleCompanion} value={trip.returnTripCompanion}/>} label="Begleitperson" />
                            <FormControlLabel disabled={trip && trip.id} className='field' control={<Checkbox onChange={returnTripToggleInfection} value={trip.returnTripInfection}/>} label="Infektion" />
                            <ToolTextField fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' value={trip.returnTripCostUnit} onChange={returnTripCostUnitOnChangeHandler} select label='Kostenträger' helperText="optional">
                                <MenuItem key={1} value={1}>Patient</MenuItem>
                                <MenuItem key={2} value={2}>Krankenkasse</MenuItem>
                            </ToolTextField>
                            {trip && trip.returnTripCostUnit && trip.returnTripCostUnit === 2 ?
                                <Autocomplete
                                    disabled={trip && trip.id}
                                    inputProps={defaultInputProps}
                                    freeSolo
                                    options={[
                                        "Techniker Krankenkasse",
                                        "Barmer Ersatzkasse",
                                        "DAK-Gesundheit",
                                        "AOK",
                                        "IKK classic",
                                        "Kaufmännische Krankenkasse",
                                        "Knappschaft",
                                        "Siemens-Betriebskrankenkasse",
                                        "Handelskrankenkasse",
                                        "Audi BKK"
                                    ]}
                                    className="field"
                                    value={trip?.returnTripHealthInsurance || ""}
                                    onChange={(event, newValue) => {
                                        if (typeof newValue === "string") {
                                            setTrip({ ...trip, returnTripHealthInsurance: newValue });
                                        } else if (newValue && newValue.inputValue) {
                                            setTrip({ ...trip, returnTripHealthInsurance: newValue.inputValue });
                                        } else {
                                            setTrip({ ...trip, returnTripHealthInsurance: newValue });
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <ToolTextField
                                            {...params}
                                            fullWidth
                                            error={validate && (!trip || (trip.costUnit === 2 && !trip.returnTripHealthInsurance))}
                                            onChange={(e) => setTrip({ ...trip, returnTripHealthInsurance: e.target.value })}
                                            label="Krankenkasse"
                                        />
                                    )}
                                /> : null}
                            <ToolTextField error={validate && (!trip || !trip.returnTripTransportKind)} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' value={trip.returnTripTransportKind} onChange={returnTripTransportKindOnChangeHandler} select label='Transportart'>
                                <MenuItem key={1} value={1}>Tragestuhl</MenuItem>
                                <MenuItem key={2} value={2}>liegend</MenuItem>
                                {props.station && props.station.walkable ? <MenuItem key={3} value={3}>gefähig</MenuItem> : null}
                            </ToolTextField>
                            <ToolTextField error={validate && (!trip || !trip.returnTripTripType)} fullWidth inputProps={defaultInputProps} disabled={trip && trip.id} className='field' value={trip.returnTripTripType} onChange={returnTripTripTypeOnChangeHandler} select label='Fahrttyp'>
                                <MenuItem key={1} value={1}>Einweisung</MenuItem>
                                <MenuItem key={2} value={2}>Entlassung</MenuItem>
                                <MenuItem key={3} value={3}>Sonstiges</MenuItem>
                            </ToolTextField>
                            <ToolTextField fullWidth disabled={trip && trip.id} multiline inputProps={multilineInputProps} minRows={7} className='multiline' label="Kommentar (optional)" value={trip.returnTripComment} onChange={returnTripCommentOnChangeHandler}/>
                        </div>
                    </div> : null}
            </DialogContent>
            <DialogActions>
                {!props.sidepanel && trip && !trip.changeRequest && props.type === "trip-request" && props.user && (props.user.role === "ADMIN" || props.user.role === "USER") ?
                    <ToolButton disabled={trip && !trip.id} className='button'
                                onClick={showRejectTripRequestDialog}>Ablehnen</ToolButton> : null}

                {!props.sidepanel && trip && trip.changeRequest && (props.type === "trip-request" || props.type === "trip-series") ?
                    <p className="info-text">Laufende Änderungsanfrage</p> : null}
                {!props.sidepanel && trip && !trip.changeRequest && props.user && (((props.user.role === "ADMIN" || props.user.role === "USER") && props.type === "trip-request") || ((props.user.role === "HOSPITAL_STAFF" || props.user.role === "HOSPITAL_ADMIN") && props.type === "trip-series")) ?
                    <ToolButton disabled={(trip && !trip.id && props.type !== "trip-series")} className='button'
                                onClick={showChangeRequestDialog}>Änderungsanfrage</ToolButton> : null}
                {!props.sidepanel && trip && !trip.changeRequest && props.user && (((props.user.role === "ADMIN" || props.user.role === "USER") && props.type === "trip-request")) ?
                    <ToolButton disabled={trip && !trip.id} className='button' main
                                onClick={showConfirmTripRequestDialog}>Bestätigen</ToolButton> : null}

                {!props.sidepanel && props.type === "trip-request" && props.user && (props.user.role === "HOSPITAL_STAFF" || props.user.role === "HOSPITAL_ADMIN") && trip && !trip.id ?
                    <ToolButton className='button' main
                                onClick={() => saveTripRequestOnClickHandler(trip)}>Anfragen</ToolButton> : null}
                {!props.sidepanel && trip && trip.changeRequest && props.user && (((props.user.role === "HOSPITAL_STAFF" || props.user.role === "HOSPITAL_ADMIN") && props.type === "trip-request") || ((props.user.role === "ADMIN" || props.user.role === "USER") && props.type === "trip-series")) ?
                    <ToolButton className='button' onClick={showRejectChangedTripRequestDialog}>Änderungsanfrage
                        ablehnen</ToolButton> : null}
                {!props.sidepanel && trip && trip.changeRequest && props.user && (((props.user.role === "HOSPITAL_STAFF" || props.user.role === "HOSPITAL_ADMIN") && props.type === "trip-request") || ((props.user.role === "ADMIN" || props.user.role === "USER") && props.type === "trip-series")) ?
                    <ToolButton className='button' main onClick={showConfirmChangedTripRequestDialog}>Änderungsanfrage
                        bestätigen</ToolButton> : null}

                {!props.sidepanel && trip && trip.returnTrip && !trip.firstTripCompleted && props.type === "trip" && props.user && (props.user.role === "ADMIN" || props.user.role === "USER") ?
                    <ToolButton disabled={trip && !trip.id} className='button'
                                onClick={showCompleteFirstTripOnlyDialog}>Hinfahrt Abschließen</ToolButton> : null}
                {!props.sidepanel && props.type === "trip" && props.user && (props.user.role === "ADMIN" || props.user.role === "USER") ?
                    <ToolButton disabled={trip && !trip.id} className='button' main
                                onClick={showCompleteTripDialog}>{trip && trip.returnTrip ? "Komplett " : null}Abschließen</ToolButton> : null}
            </DialogActions>
        </Dialog>
    )
}

export default TripDialog;