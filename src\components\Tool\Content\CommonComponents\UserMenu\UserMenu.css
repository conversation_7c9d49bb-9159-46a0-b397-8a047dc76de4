.user-menu {
    position:absolute;
    top:0;
    right:0;
    left:0;
    bottom:85%;
    overflow:hidden;
}

.user-menu-controls {
    position: absolute;
    right: 48px;
    bottom: 48px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-menu h2 {
    cursor: pointer;
    margin: 0;
}

.user-menu .station-select {
    position:absolute;
    right:48px;
    bottom:8px;
    width:160px;
    text-align:left;
}

.user-menu .MenuPanel {
    position: absolute;
    right: 32px;
    top: 70%;
    background-color: var(--background-primary);
    box-shadow: 0 0 5px 1px var(--shadow-color);
    z-index: 30;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.user-menu .active-display {
    position: absolute;
    left:1%;
    top:48px;
}

.user-menu ul li {
    cursor: pointer;
    width:256px;
    margin:8px 0;
}

@media screen and (max-width: 1023px) {
    .user-menu {
        bottom:85%;
    }
}