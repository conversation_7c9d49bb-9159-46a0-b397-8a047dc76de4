{"name": "warehouse-manager", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@stomp/stompjs": "^7.1.1", "assert": "^2.1.0", "async-hooks": "^1.3.1", "axios": "^1.7.2", "body-parser": "^1.19.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "cors": "^2.8.5", "crypto-browserify": "^3.12.0", "dompurify": "^3.0.6", "event-source-polyfill": "^1.0.31", "eventsource-parser": "^3.0.2", "express": "^4.18.2", "html2canvas": "^1.4.1", "https-browserify": "^1.0.0", "immutable": "^4.1.0", "js-cookie": "^2.2.1", "jschardet": "^3.1.3", "jspdf": "^3.0.1", "lodash": "^4.17.21", "markdown-to-jsx": "^7.7.7", "os-browserify": "^0.3.0", "path": "^0.12.7", "pdf-lib": "^1.17.1", "pdfjs": "^2.4.7", "pdfjs-dist": "^4.3.136", "process": "^0.11.10", "query-string": "^9.0.0", "querystring-es3": "^0.2.1", "react": "^18.2.0", "react-confirm-alert": "^2.6.1", "react-csv": "^2.0.3", "react-dom": "^18.2.0", "react-dropzone": "^11.2.4", "react-pdf": "^9.0.0", "react-router-dom": "^5.2.0", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^6.0.8", "react-tooltip": "^5.27.0", "react-webcam": "^7.2.0", "recharts": "^2.15.1", "sockjs-client": "^1.6.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "styled-components": "^5.3.6", "url": "^0.11.3", "vm-browserify": "^1.1.2", "web-push": "^3.4.4", "webpack": "^5.92.1"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.18.6", "@vitejs/plugin-react": "^4.5.1", "gh-pages": "^6.1.1", "husky": "^4.2.5", "jest-environment-jsdom-sixteen": "^1.0.3", "lint-staged": "^10.2.11", "papaparse": "^5.4.1", "typescript": "^4.0.2", "vite": "^6.3.5"}}