.history-panel {
    position:absolute;
    right:0;
    top:0;
    bottom:0;
    display:flex;
    z-index:500;
    overflow:visible;
}

.history-panel .toggle-button {
    width:64px;
    height:64px;
    display:flex;
    text-align:center;
    margin-left:-64px;
    margin-top:512px;
    z-index:500;
}

.history-panel .history-icon {
    color:white;
    width:40px;
    height:40px;
    margin:auto;
}

.history-panel .content-li {
    overflow-x:hidden;
}

.history-panel .content-holder {
    height:100%;
    width:512px;
    position: relative;
}

.history-panel .history-entries {
    width:100%;
    position:absolute;
    top:128px;
    bottom:0;
    overflow-y:auto;
}

.history-panel .field {
    width:160px;
    float:left;
    margin:32px 0 0 8px;
}

.history-panel .search-button {
    float:left;
    margin:32px 0 0 8px;
    display:inline-block;
}

.history-panel .info-text {
    float:left;
    width:100%;
    text-align:left;
    margin:4px 0 0 8px;
    font-size:11px;
    color:gray;
}

.history-panel .search-bar {
    width:100%;
    height:128px;
}

@media screen and (max-width: 1024px) {
    .history-panel {
        display:none;
    }
}