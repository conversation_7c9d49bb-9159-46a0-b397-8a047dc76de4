import React from "react";
import './Administration.css';
import {Route} from "react-router-dom";

//Components
import UserAdministration from "./UserAdministration/UserAdministration";
import CompanyAdministration from "./CompanyAdministration/CompanyAdministration";
import DataSettings from "./DataSettings/DataSettings";

const Administration = (props) => {
    return (
        <div className='administration'>
            <Route exact path='/administration'>
                <UserAdministration
                    showMessage={props.showMessage}
                    setLoading={props.setLoading}
                    company={props.company}
                    user={props.user}

                    //Users
                    users={props.users}
                    addNewUser={props.addNewUser}
                    updateUser={props.updateUser}
                    deleteUser={props.deleteUser}
                    changePassword={props.changePassword}

                    //Locations
                    locations={props.locations}

                    // Hospitals
                    hospitals={props.hospitals}

                    // Stations
                    stations={props.stations}
                />
            </Route>
            <Route exact path='/administration/company-administration'>
                <CompanyAdministration
                    showMessage={props.showMessage}
                    countries={props.countries}
                    articleGroups={props.articleGroups}
                    setLoading={props.setLoading}

                    //Company
                    company={props.company}
                    updateCompany={props.updateCompany}

                    //Accounts
                    accounts={props.accounts}
                />
            </Route>
            <Route exact path='/administration/data-settings'>
                <DataSettings
                    showMessage={props.showMessage}
                    countries={props.countries}
                    articleGroups={props.articleGroups}
                    setLoading={props.setLoading}
                    downloadFile={props.downloadFile}

                    //Company
                    company={props.company}
                    updateCompany={props.updateCompany}

                    //Accounts
                    accounts={props.accounts}
                />
            </Route>
        </div>
    )
}

export default Administration;