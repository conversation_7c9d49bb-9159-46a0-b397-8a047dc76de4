import React, { useEffect, useRef, useState} from "react";
import './Chat.css';
import ToolTextField from "../../../../../../reusable/ToolField/ToolTextField";
import ToolButton from "../../../../../../reusable/ToolButton/ToolButton";
import api from "../../../../../../api";
import Message from "./Message/Message";
import ToolPaper from "../../../../../../reusable/ToolPaper/ToolPaper";
import { createParser } from 'eventsource-parser';

//Components

const Chat = (props) => {

    const [text,setText] = useState(null)
    const [messages,setMessages] = useState([])
    const [thinking,setThinking] = useState(false)
    const [typing,setTyping] = useState(false)

    const messagesContainerRef = useRef(null);

    const _handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Shift + Enter: nichts senden, erlaubt Zeilenumbruch
                return;
            }
            if (e.ctrlKey || !e.shiftKey) {
                e.preventDefault();
                sendOnClickHandler();
            }
        }
    }

    useEffect(() => {
        if(props.conversation && props.conversation.id) {
            loadConversation(props.conversation)
        }else if(!props.conversation){
            setMessages([])
        }
    }, [props.conversation]);

    useEffect(() => {
        scrollDown();
    }, [messages]);

    const sendOnClickHandler = () => {
        if(props.conversation) {
            sendChatMessage(props.conversation)
        }else{
            startConversation()
        }
    }

    const loadConversation = (conversation) => {
        props.setLoading(true)
        const url = '/conversations/' + conversation.id;
        api.get(url,conversation)
            .then(response => {
                if(response.data.length > 0) {
                    setMessages(response.data)
                }
                props.setLoading(false)
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
                props.setLoading(false)
            })
    }

    const sendChatMessage = async (conversation) => {
        setThinking(true)
        setTyping(true)
        if (!text) return;

        const userMessage = {
            sender: props.user,
            receiver: null,
            text: text,
            conversation: conversation
        };

        const messageDto = {
            message: userMessage,
            messages: messages
        };

        const updatedMessages = [...messages, userMessage];
        setMessages(updatedMessages);
        setText(null);

        try {
            const res = await fetch(import.meta.env.VITE_API_URL + '/chat-service/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': localStorage.getItem("token")
                },
                body: JSON.stringify(messageDto)
            });
            const reader = res.body.getReader();
            const decoder = new TextDecoder();
            let finished = false
            const parser = createParser({
                onEvent: (event) => {

                    // 1. Handle incoming text tokens
                    if (event.event === 'inference_token') {
                        setThinking(false);
                        try {
                            // The data is a JSON string like '{"value": "text"}', so we parse it...
                            const parsedData = JSON.parse(event.data);
                            // ...and then extract the actual token from the 'value' property.
                            const token = parsedData.value;
                            
                            if (token !== undefined) {
                                // Append the new token to the mutable aiMessage object.
                                aiMessage.text += token;
                                // Update the state to re-render the message.
                                setMessages([...updatedMessages, { ...aiMessage }]);
                            }
                        } catch (e) {
                            console.error("Failed to parse inference_token data:", event.data);
                        }
                    } 
                    // 2. Handle the end-of-stream signal
                    else if (event.event === 'eos' || event.event === 'stream_end') {
                        // The 'eos' or 'stream_end' event tells us the stream is done.
                        finished = true;
                        setThinking(false);
                        setTyping(false);
                    }

                    // Other events like 'worker_assigned' and 'sources' will be logged but otherwise ignored for now.
                }

            });

            let aiMessage = {
                sender: null,
                receiver: props.user,
                text: "",
                conversation: conversation
            };

            while (!finished) {
                const { done, value } = await reader.read();
                if (done) break;
                parser.feed(decoder.decode(value));
            }
            setMessages([...updatedMessages, aiMessage]);
        } catch (error) {
            setThinking(false)
            setTyping(false)
        }
    }

    const startConversation = () => {
        console.log("in startConversation")
        if(!text) {
            props.showMessage(2,"Geben Sie zuerst eine Nachricht ein")
            return;
        }
        props.setLoading(true)
        const url = '/conversations';
        let message = {
            sender:props.user,
            receiver:null,
            text:text
        }
        api.post(url,message)
            .then(response => {
                props.addConversationToList(response.data)
                props.setConversation(response.data)
                props.setLoading(false)
                sendChatMessage(response.data)
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
                props.setLoading(false)
            })
    }

    const scrollDown = () => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    };

    return (
        <div className='chat' onKeyDown={_handleKeyDown}>
            {props.conversation ? <div id="chat-messages" className="chat-messages" ref={messagesContainerRef}>
                {messages
                    ?.slice()
                    .sort((a, b) => {
                        if (a.id === null) return 1;
                        if (b.id === null) return -1;
                        return a.id - b.id;
                    })
                    .map(message => (
                        <Message
                            key={message.id ?? `temp-${Math.random()}`}
                            message={message}
                            user={props.user}
                        />
                    ))}
            </div> : null}
            {thinking ? <ToolPaper className="thinking-paper blink-infinite black">denkt nach...</ToolPaper> : null}
            <ToolTextField onChange={(e) => setText(e.target.value)} value={text ? text : ""} rows={5} multiline
                           placeholder="Nachricht" className="textfield chat-textfield"/>
            <ToolButton onClick={sendOnClickHandler} disabled={thinking || typing} main className="button send-button">Senden</ToolButton>
        </div>
    )
}

export default Chat;