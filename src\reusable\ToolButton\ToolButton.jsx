import React, {Component, useState} from "react";
import "./ToolButton.css";

//Components

const ToolButton = (props) => {

    return (
        <div style={{...props.style, opacity: props.disabled ? 0.5 : props.style?.opacity}} className={['tool-button animate',props.className,props.main ? props.negative ? 'main-negative-button' : 'main-button' : props.negative ?  'secondary-negative-button' : 'secondary-button'].filter(Boolean).join(' ')} onClick={props.onClick}>
            <p className="white">{props.children}</p>
        </div>
    )
}

export default ToolButton;