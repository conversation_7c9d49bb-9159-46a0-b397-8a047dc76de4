import React, { useState, useEffect } from 'react';
import './ArticleStockItem.css';

// Components
import HelperFunctions from "../HelperFunctions";
import ToolPaper from "../ToolPaper/ToolPaper";

const ArticleStockItem = (props) => {

    return (
        <ToolPaper elevation={3} className='article-stock-item'>

            <ul className="information-holder">
                <li className="option-li">
                    <p
                        data-tooltip-id="article-stock-tooltip"
                        data-tooltip-place="top"
                        data-tooltip-content={props.articleStock?.article?.nr.length > 12 ? props.articleStock.article.nr : ""}
                    >{props.articleStock?.article?.nr ? HelperFunctions.formatString(props.articleStock.article.nr, 12) : "-"}</p>
                </li>
                <li className="option-li">
                    <p
                        data-tooltip-id="article-stock-tooltip"
                        data-tooltip-place="top"
                        data-tooltip-content={props.articleStock?.article?.name.length > 12 ? props.articleStock.article.name : ""}
                    >{props.articleStock?.article?.name ? HelperFunctions.formatString(props.articleStock.article.name, 12) : "-"}</p>
                </li>
                <li className="option-li">
                    <p
                        data-tooltip-id="article-stock-tooltip"
                        data-tooltip-place="top"
                        data-tooltip-content={props.articleStock?.article?.refNr.length > 12 ? props.articleStock.article.refNr : ""}
                    >{props.articleStock?.article?.refNr ? HelperFunctions.formatString(props.articleStock.article.refNr, 12) : "-"}</p>
                </li>
                <li className="option-li">
                    <p
                        data-tooltip-id="article-stock-tooltip"
                        data-tooltip-place="top"
                        data-tooltip-content={props.articleStock?.article?.pcn.length > 12 ? props.articleStock.article.pcn : ""}
                    >{props.articleStock?.article?.pcn ? HelperFunctions.formatString(props.articleStock.article.pcn, 12) : "-"}</p>
                </li>
                <li className="option-li">
                    <p>{props.articleStock?.inventory?.inventoryCount? props.articleStock.inventory.inventoryCount : "0"}</p>
                </li>
            </ul>
        </ToolPaper>
    );
};

export default ArticleStockItem;