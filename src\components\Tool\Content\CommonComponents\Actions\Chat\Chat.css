.chat {
    display: inline-block;
    overflow: hidden;
    background-color: var(--background-primary);
    color: var(--text-primary);
}

.chat .chat-textfield {
    position:absolute;
    bottom:0;
    left:8px;
    right:128px;
    height:128px;
}

.chat .send-button {
    position:absolute;
    right:28px;
    bottom:16px;
}

.chat .chat-messages {
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:128px;
    overflow-y:auto;
}

.chat .load-icon {
    color: var(--text-white);
}

.chat .test-button{
    position:absolute;
    top:100px;
    left:100px;
}

.chat .thinking-paper {
    position:absolute;
    bottom:132px;
    left:8px;
    padding:4px 8px;
}