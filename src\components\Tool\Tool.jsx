import React, { useState, useEffect } from "react";
import './Tool.css';
import 'react-confirm-alert/src/react-confirm-alert.css';
import api from "../../api";

//Components
import MenuItem from "@mui/material/MenuItem";
import '../../fonts/OpenSans-Regular-normal';
import '../../fonts/OpenSans-Bold-normal';
import HelperFunctions from "../../reusable/HelperFunctions";
import LoadingScreen from "./Content/CommonComponents/LoadingScreen/LoadingScreen";
import Content from "./Content/Content";
import {useHistory} from "react-router-dom";

const Tool = (props) => {

    const [company,setCompany] = useState(null)
    const [user,setUser] = useState(null)
    const [users,setUsers] = useState(null)
    const [conversation,setConversation] = useState(null)
    const [conversations,setConversations] = useState(null)

    useEffect(() => {
        loadUser()
        loadCompany()
    }, []);

    const history = useHistory()

    const loadData = (loadRequest,recentDataLoadRequest) => {
        const url = '/load-data';
        api.post(url,loadRequest)
            .then(response => {
                if(response.data.company !== null) {
                    setCompany(response.data.company)
                    console.log("[Tool.loadData()]: Unternehmen geladen");
                }
                if(response.data.user !== null) {
                    setUser(response.data.user)
                    console.log("[Tool.loadData()]: Aktuellen Benutzer geladen");
                }
                if(response.data.users) {
                    setUsers(response.data.users)
                    console.log("[Tool.loadData()]: Benutzer geladen");
                }
                loadRecentData(recentDataLoadRequest)
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
                props.setLoading(false);
            })
    }

    const loadRecentData = (recentDataLoadRequest) => {
        const url = '/load-data/recent';
        api.post(url,recentDataLoadRequest)
            .then(response => {
                console.log(response.data)
                if(response.data.conversations) {
                    setConversations(response.data.conversations)
                    console.log("[Tool.loadRecentData()]: Konversationen geladen");
                }
            })
            .catch(error => {
                props.logout();
            });
    }

    //Loading

    const loadCompany = () => {
        const url = '/company';
        api.get(url)
            .then(response => {
                setCompany(response.data)
            })
            .catch(error => {
                props.logout();
            });
    }

    const loadUser = () => {
        const url = '/users/current';
        api.get(url)
            .then(response => {
                setUser(response.data)
            })
            .catch(error => {
                console.log("Error in loadUser")
                console.log(error)
                props.logout();
            });
    }

    const loadUsers = () => {
        const url = '/users';
        api.get(url)
            .then(response => {
                setUsers(response.data)
            })
            .catch(error => {
                props.logout();
            });
    }

    //List changes

    const addNewUser = (user) => {
        const url = '/users';
        api.post(url,user)
            .then(response => {
                console.log(response.data)
                addUserToList(response.data);
                props.showMessage(0,'Benutzer erfolgreich angelegt')
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
            });
    }

    const updateUser = (user) => {
        const url = '/users';
        api.put(url,user)
            .then(response => {
                updateUserInList(response.data);
                props.showMessage(0,"Änderungen erfolgreich gespeichert")
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
            });
    }

    const deleteUser = (user) => {
        api.delete("/users/" + user.id)
            .then(response => {
                props.showMessage(0,"Benutzer erfolgreich gelöscht");
                removeUserFromList(user.id);
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                    props.logout();
                }
            });
    }

    const addUserToList = (user) => {
        let updatedUsers = [ ...users ];
        updatedUsers.push(user);
        setUsers(updatedUsers)
    }

    const updateUserInList = (user) => {
        let updatedUsers = [ ...users ];
        for(let i = 0; i < updatedUsers.length; i++) {
            if(updatedUsers[i].id === user.id) {
                updatedUsers[i] = user;
                break;
            }
        }
        setUsers(updatedUsers)
    }

    const removeUserFromList = (id) => {
        let updatedUsers = [ ...users ];
        for(let i = 0; i < updatedUsers.length; i++) {
            if (updatedUsers[i].id === id) {
                updatedUsers.splice(i,1)
                setUsers(updatedUsers)
                return;
            }
        }
    }

    const downloadFile = (fileStorageLocation) => {
        props.setLoading(true);
        const url = '/file-storage';
        api.post(url,fileStorageLocation,{responseType:'blob'})
            .then(response => {
                const pdfFile = new Blob([response.data], { type: response.data.type });
                const pdfFileURL = URL.createObjectURL(pdfFile);
                const downloadLink = document.createElement('a');
                downloadLink.href = pdfFileURL;
                downloadLink.download = fileStorageLocation.name || 'download.pdf'; // Set default name if fileStorageLocation.name is not available
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(pdfFileURL); // Clean up the URL object
                props.setLoading(false);
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                }
                props.setLoading(false);
            });
    }

    //Settings

    const changePassword = (masterPassword,user) => {
        let masterUser = {
            username:localStorage.getItem('abbreviation') + '.' + localStorage.getItem('username'),
            password:masterPassword
        }
        const loginUrl = '/login';
        api.post(loginUrl,masterUser)
            .then(response => {
                const url = '/users/change-password';
                api.put(url,user)
                    .then(response => {
                        props.showMessage(0,'Passwort erfolgreich geändert');
                    })
                    .catch(error => {
                        console.log(error);
                        if(error.response && error.response.data) {
                            props.showMessage(2,error.response.data);
                        } else if(!error.response && error.request) {
                            props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                        } else {
                            props.showMessage(2,"Etwas ist schiefgelaufen");
                        }
                    });
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                }
            });
    }

    const changeOwnPassword = (changeOwnPasswordRequest) => {
        const url = '/users/change-own-password';
        api.put(url,changeOwnPasswordRequest)
            .then(response => {
                props.showMessage(0,'Passwort erfolgreich geändert');
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                }
            });
    }

    const setConversationAndHistory = (conversation) => {
        setConversation(conversation)
        history.push("/chat")
    }

    const updateCompany = (company) => {
        const url = '/company';
        api.put(url,company)
            .then(response => {
                props.showMessage(0,'Unternehmensinformationen erfolgreich geändert');
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                }
            });
    }

    const deleteConversation = (deleteConversation) => {
        const url = '/conversations/' + deleteConversation.id
        api.delete(url)
            .then(response => {
                removeConversationFromList(deleteConversation.id)
                if(conversation?.id === deleteConversation.id) {
                    setConversation(null)
                }
                props.showMessage(0,'Konversation wurde erfolgreich gelöscht');
            })
            .catch(error => {
                console.log(error);
                if(error.response && error.response.data) {
                    props.showMessage(2,error.response.data);
                } else if(!error.response && error.request) {
                    props.showMessage(2,"Es kann keine Verbindung zum Server aufgebaut werden");
                } else {
                    props.showMessage(2,"Etwas ist schiefgelaufen");
                }
            });
    }

    const addConversationToList = (conversation) => {
        let updatedConversations = [ ...conversations ];
        updatedConversations.push(conversation);
        setConversations(updatedConversations)
    }

    const updateConversationInList = (conversation) => {
        let updatedConversations = [ ...conversations ];
        for(let i = 0; i < updatedConversations.length; i++) {
            if(updatedConversations[i].id === conversation.id) {
                updatedConversations[i] = conversation;
                break;
            }
        }
        setConversations(updatedConversations)
    }

    const removeConversationFromList = (id) => {
        let updatedConversations = [ ...conversations ];
        for(let i = 0; i < updatedConversations.length; i++) {
            if (updatedConversations[i].id === id) {
                updatedConversations.splice(i,1)
                setConversations(updatedConversations)
                return;
            }
        }
    }

    return (
        <div className='tool'>

            {user && company ? <Content
                //Common
                loadData={loadData}
                showMessage={props.showMessage}
                setLoading={props.setLoading}
                screenSize={props.screenSize}
                downloadFile={downloadFile}

                //User
                user={user}
                changePassword={changePassword}
                changeOwnPassword={changeOwnPassword}
                logout={props.logout}

                //Company
                company={company}
                updateCompany={updateCompany}

                //Users
                users={users}
                loadUsers={loadUsers}
                addNewUser={addNewUser}
                updateUser={updateUser}
                deleteUser={deleteUser}

                // Conversations
                conversation={conversation}
                conversations={conversations}
                setConversation={setConversationAndHistory}
                addConversationToList={addConversationToList}
                updateConversationInList={updateConversationInList}
                deleteConversation={deleteConversation}
                removeConversationFromList={removeConversationFromList}
            /> : <LoadingScreen/>}
        </div>
    )
}

export default Tool;